package com.cnpc.ymz.bff.cbase.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * 抽象定时任务基类，提供定时任务执行的通用处理逻辑。
 * <p>
 * 该类继承自JavaProcessor，定义了定时任务执行的基本流程，包括日志设置、任务执行和异常处理。
 * 子类需实现doJob方法以定义具体的任务逻辑。
 *
 * @return ProcessResult 任务执行结果，包含执行状态和可能的错误信息。
 * <p>
 * 方法流程：
 * 1. 从上下文中提取任务跟踪ID、参数和客户端代码。
 * 2. 设置任务执行的日志信息。
 * 3. 调用抽象方法doJob执行具体任务逻辑。
 * 4. 捕获执行过程中的异常，记录错误日志并返回失败结果。
 * 5. 任务成功执行时返回成功结果。
 * <p>
 * 注意：
 * - 使用MDC（Mapped Diagnostic Context）来存储和传递日志信息。
 * - 日志格式为：用户ID|跟踪ID|客户端代码。
 */
@Slf4j
public abstract class AbstractScheduleJob extends JavaProcessor {

    protected abstract void doJob(String jobParameters);

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String traceId = String.valueOf(context.getJobInstanceId());
        String jobParameters = context.getJobParameters();
        String clientCode = String.valueOf(context.getJobId());
        setLog(context.getJobName(), traceId, clientCode);
        log.info("触发定时任务,开始执行..");
        try {
            doJob(jobParameters);
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("定时任务执行失败", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            MDC.clear();
        }
    }

    private void setLog(String userId, String traceId, String clientCode) {
        String logInfo = userId + "|" +
                traceId + "|" +
                clientCode;
        MDC.remove("LOG");
        MDC.put("LOG", logInfo);
    }
}
