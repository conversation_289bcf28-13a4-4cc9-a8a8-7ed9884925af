<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cnpc.ymz.bff</groupId>
        <artifactId>kld-cbase</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>

    <artifactId>cbase-job</artifactId>
    <version>1.2.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.cnpc.ymz.bff</groupId>
            <artifactId>cbase-common</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.cnpc.ymz.bff</groupId>
            <artifactId>cbase-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>