package com.cnpc.ymz.bff.cbase.web.http;

import java.util.HashMap;
import java.util.Map;

public class HttpRequest {

    private String contentType;

    private String contentEncoding;

    private Map<String, String> header;

    private String body;

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getContentEncoding() {
        return contentEncoding;
    }

    public void setContentEncoding(String contentEncoding) {
        this.contentEncoding = contentEncoding;
    }

    public Map<String, String> getHeader() {
        return header;
    }

    public void setHeader(Map<String, String> header) {
        this.header = header;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    private HttpRequest() {

    }

    private HttpRequest(HttpRequestBuilder builder) {
        this.header = builder.header;
        this.body = builder.body;
        this.contentType = builder.contentType;
        this.contentEncoding = builder.contentEncoding;
    }

    public static class HttpRequestBuilder {

        private String contentType = "application/json";

        private String contentEncoding = "UTF-8";

        private Map<String, String> header = new HashMap<>();

        private String body;

        public HttpRequest build() {
            return new HttpRequest(this);
        }

        public HttpRequestBuilder header(Map<String, String> map) {
            this.header = map;
            return this;
        }

        public HttpRequestBuilder body(String var) {
            this.body = var;
            return this;
        }

        public HttpRequestBuilder contentType(String var) {
            this.contentType = var;
            return this;
        }

        public HttpRequestBuilder contentEncoding(String var) {
            this.contentEncoding = var;
            return this;
        }
    }

}
