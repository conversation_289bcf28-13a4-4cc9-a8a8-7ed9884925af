package com.cnpc.ymz.bff.cbase.web.inteceptor;

import com.alibaba.nacos.shaded.javax.annotation.Nonnull;
import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.HeaderUtils;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Component
@Slf4j
public class InterceptorCNPC implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        ContextInfo contextInfo = new ContextInfo();
        contextInfo.setIp(request.getHeader("orig-ip"));
        contextInfo.setClientCode(request.getHeader("client-code"));
        contextInfo.setSourceCode(request.getHeader("source-code"));
        contextInfo.setTime(request.getHeader("time"));
        contextInfo.setDeviceId(request.getHeader("device-id"));
        contextInfo.setDeviceNo(request.getHeader("device-no"));
        contextInfo.setApiVersion(request.getHeader("api-version"));
        String deviceName = request.getHeader("device-name");
        if (StringUtils.isNotBlank(deviceName)) {
            contextInfo.setDeviceName(HeaderUtils.decode(deviceName));
        }
        String riskField = request.getHeader("risk-field");
        if (StringUtils.isNotBlank(riskField)) {
            contextInfo.setRiskField(HeaderUtils.decode(riskField));
        }
        contextInfo.setUserId(request.getHeader("user-info"));
        contextInfo.setTraceId(request.getHeader("trace-id"));
        contextInfo.setAuthorization(request.getHeader("authorization"));
        String userName = request.getHeader("user-name");
        if (StringUtils.isNotBlank(userName)) {
            contextInfo.setUserName(HeaderUtils.decode(userName));
        }
        ContextUtils.setContext(contextInfo);
        log.debug("通用拦截器处理,成功设置上下文对象");
        return true;
    }

    @Override
    public void afterCompletion(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response, @Nonnull Object handler,
                                Exception ex) throws Exception {
        ContextUtils.removeContext();
        log.debug("通用拦截器处理,成功清除上下文对象");
        MDC.clear();
    }
}
