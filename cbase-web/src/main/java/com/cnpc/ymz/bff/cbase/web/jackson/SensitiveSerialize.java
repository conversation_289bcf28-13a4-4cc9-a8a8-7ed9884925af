package com.cnpc.ymz.bff.cbase.web.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * 脱敏处理器
 *
 * <AUTHOR>
 * @date 2025/4/30 09:10
 * @company 昆仑数智科技有限责任公司
 */
public class SensitiveSerialize extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (StringUtils.isBlank(value)) {
            jsonGenerator.writeString("");
            return;
        }

        // 1. 处理身份证号（18位或15位）
        if (isIdCardNumber(value)) {
            jsonGenerator.writeString(maskIdCard(value));
            return;
        }

        // 2. 处理手机号（11位数字）
        if (isPhoneNumber(value)) {
            jsonGenerator.writeString(maskPhone(value));
            return;
        }

        // 3. 处理中文姓名（2-6个中文字符）
        if (isChineseName(value)) {
            jsonGenerator.writeString(maskChineseName(value));
            return;
        }

        // 4. 处理银行卡号（16-19位数字）
        if (isBankCardNumber(value)) {
            jsonGenerator.writeString(maskBankCard(value));
            return;
        }

        // 其余默认情况，只保留第一个字符
        jsonGenerator.writeString(maskDefault(value));
    }

    private boolean isIdCardNumber(String value) {
        // 匹配15位或18位身份证号（数字结尾可能有X）
        return value.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$")
                || value.matches("^[1-9]\\d{5}\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}$");
    }

    private String maskIdCard(String idCard) {
        if (idCard.length() == 18) {
            return "**************" + idCard.substring(14);
        } else if (idCard.length() == 15) {
            return "**********" + idCard.substring(11);
        }
        return maskDefault(idCard); // 如果不是标准长度，按照默认方式处理
    }

    private boolean isPhoneNumber(String value) {
        // 匹配11位手机号（1开头）
        return value.matches("^1[3-9]\\d{9}$");
    }

    private String maskPhone(String phone) {
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    private boolean isChineseName(String value) {
        // 匹配2-6个中文字符
        return value.matches("^[\u4e00-\u9fa5]{2,6}$");
    }

    private String maskChineseName(String name) {
        if (name.length() <= 2) {
            return "*" + name.substring(name.length() - 1);
        }
        return "*" + name.substring(name.length() - 2);
    }

    private boolean isBankCardNumber(String value) {
        // 匹配16-19位数字
        return value.matches("^\\d{16,19}$");
    }

    private String maskBankCard(String bankCard) {
        int length = bankCard.length();
        if (length >= 4) {
            return StringUtils.repeat("*", length - 4) + bankCard.substring(length - 4);
        }
        return maskDefault(bankCard); // 如果长度不足4位，按照默认方式
    }

    private String maskDefault(String value) {
        if (value.length() <= 1) {
            return value;
        }
        return value.charAt(0) + StringUtils.repeat("*", value.length() - 1);
    }
}