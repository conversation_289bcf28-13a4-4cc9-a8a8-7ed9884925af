package com.cnpc.ymz.bff.cbase.web.log;


import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;


public class LogAdapter {

    /**
     * 设置日志LOG字段信息
     */
    public static void setLog() {
        StringBuilder logInfo = new StringBuilder();
        ContextInfo contextInfo = ContextUtils.getContext();
        String userId = contextInfo.getUserId();
        if (StringUtils.isBlank(userId)) {
            userId = "";
        }
        logInfo.append(userId).append("|");
        String traceId = contextInfo.getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = "";
        }
        logInfo.append(traceId).append("|");
        String clientCode = contextInfo.getClientCode();
        if (StringUtils.isBlank(clientCode)) {
            clientCode = "";
        }
        logInfo.append(clientCode);
        MDC.put("LOG", logInfo.toString());
    }

    public static void setLog(ServletRequest request) {
        StringBuilder logInfo = new StringBuilder();
        String userId = ((HttpServletRequest) request).getHeader("user-info");
        if (StringUtils.isBlank(userId)) {
            userId = "";
        }
        logInfo.append(userId).append("|");
        String traceId = ((HttpServletRequest) request).getHeader("trace-id");
        if (StringUtils.isBlank(traceId)) {
            traceId = "";
        }
        logInfo.append(traceId).append("|");
        String clientCode = ((HttpServletRequest) request).getHeader("client-code");
        if (StringUtils.isBlank(clientCode)) {
            clientCode = "";
        }
        logInfo.append(clientCode);
        MDC.put("LOG", logInfo.toString());
    }
}
