package com.cnpc.ymz.bff.cbase.web.util;

import com.cnpc.ymz.bff.cbase.common.utils.HeaderUtils;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class InterceptorUtils {

    public static String getOtherContext(HttpServletRequest request, String key) {
        String context = request.getHeader("other-context");
        if (StringUtils.isNotBlank(context)) {
            String str = HeaderUtils.decode(context);
            return JSONUtils.parseMap(str, String.class, String.class).get(key);
        } else {
            return "";
        }
    }
}
