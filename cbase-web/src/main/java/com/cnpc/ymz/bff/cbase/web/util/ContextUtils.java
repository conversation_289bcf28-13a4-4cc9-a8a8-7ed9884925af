package com.cnpc.ymz.bff.cbase.web.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;

/**
 * 业务上下文对象
 *
 * <AUTHOR>
 * @date 2025/1/15 10:25
 * @company 昆仑数智科技有限责任公司
 */
public class ContextUtils {

    private final static TransmittableThreadLocal<ContextInfo> INHERITABLE_THREAD_LOCAL = new TransmittableThreadLocal<>();

    public static void setContext(ContextInfo global) {
        INHERITABLE_THREAD_LOCAL.set(global);
    }

    public static ContextInfo getContext() {
        return INHERITABLE_THREAD_LOCAL.get();
    }

    public static void removeContext() {
        INHERITABLE_THREAD_LOCAL.remove();
    }
}
