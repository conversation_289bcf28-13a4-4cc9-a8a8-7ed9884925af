package com.cnpc.ymz.bff.cbase.web.feign;

import com.cnpc.ymz.bff.cbase.web.exception.BizException;
import com.cnpc.ymz.bff.cbase.web.inteceptor.FeignInterceptor;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import com.kld.foundation.utils.ResultObject;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * feign解码器，处理200请求
 *
 * <AUTHOR>
 * @date 2025/6/6 16:51
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public class CustomerDecoder implements Decoder {

    final Decoder delegate;

    public CustomerDecoder(Decoder delegate) {
        this.delegate = delegate;
    }

    /**
     * 解码过程
     * 1. 打印中台返回日志（仅对非二进制响应）。
     * 2. 中台返回异常统一处理。
     * 3. 支持文件流响应。
     */
    @Override
    public Object decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        String url = "";
        if (response.request() != null) {
            url = response.request().url();
        }

        // 获取feign调用耗时
        Map<String, String> map = ContextUtils.getContext().getOthers();
        Long time = null;
        if (map != null && map.containsKey(FeignInterceptor.FEIGN_TIME)) {
            long start = Long.parseLong(map.get(FeignInterceptor.FEIGN_TIME));
            time = System.currentTimeMillis() - start;
        }

        String contentType = response.headers().get("Content-Type").stream()
                .findFirst()
                .orElse("")
                .toLowerCase();
        if (!contentType.contains("application/json")) {
            if (time != null) {
                log.info("feign调用接口{},耗时:{}ms", url, time);
            } else {
                log.info("feign调用接口{}", url);
            }
            return delegate.decode(response, type);
        }

        // 处理JSON响应
        String resultStr = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        if (time != null) {
            log.info("feign调用接口{},耗时:{}ms,响应数据:{}", url, time, resultStr);
        } else {
            log.info("feign调用接口{},响应数据:{}", url, resultStr);
        }

        Object object = delegate.decode(response.toBuilder().body(resultStr, StandardCharsets.UTF_8).build(), type);
        ResultObject<?> resultObject = null;
        if (object instanceof ResultObject) {
            resultObject = (ResultObject<?>) object;
            if (!"0".equals(resultObject.getCode())) {
                throw new BizException(resultObject.getCode(), resultObject.getMsg());
            }
        }
        return object;
    }
}