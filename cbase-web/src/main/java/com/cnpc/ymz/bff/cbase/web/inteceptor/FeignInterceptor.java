package com.cnpc.ymz.bff.cbase.web.inteceptor;


import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import com.kld.foundation.utils.context.KldInvocationContextHolder;
import com.kld.foundation.utils.security.UserType;
import com.kld.foundation.utils.user.PetroMemberInfo;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class FeignInterceptor implements RequestInterceptor {

    public static final String FEIGN_TIME = "feignTime";

    @Value("${spring.application.name}")
    private String service;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ContextInfo contextInfo = ContextUtils.getContext();
        if (contextInfo == null) {
            log.error("上下文信息为空");
            return;
        }
        FeignHeaderDTO feignHeaderDTO = getFeignHeaderDTO(contextInfo);
        String json = JSONUtils.toJSONString(feignHeaderDTO);
        log.info("x-invocation-context(编码前):{}", json);
        String str = URLEncoder.encode(json);
        log.debug("x-invocation-context(编码后):{}", str);
        requestTemplate.header(KldInvocationContextHolder.ATTRIBUTE_KEY, str);

        // 记录feign的请求发起时间
        contextInfo.getOthers().put(FEIGN_TIME, String.valueOf(System.currentTimeMillis()));

        // 记录日志
        log.info("发起feign调用:{},请求体:{}", requestTemplate.url(),
                (requestTemplate.body() == null || requestTemplate.body().length == 0) ? "null" : new String(requestTemplate.body(), StandardCharsets.UTF_8));

    }

    private FeignHeaderDTO getFeignHeaderDTO(ContextInfo contextInfo) {
        PetroMemberInfo petroMemberInfo = new PetroMemberInfo();
        boolean login = false;
        if (StringUtils.isNotBlank(contextInfo.getUserId())) {
            petroMemberInfo.setUserId(Long.parseLong(contextInfo.getUserId()));
            petroMemberInfo.setUserType(UserType.OUTER);
            petroMemberInfo.setUsername(contextInfo.getUserName());
            login = true;
        }
        FeignHeaderDTO feignHeaderDTO = new FeignHeaderDTO();
        feignHeaderDTO.setIp(contextInfo.getIp());
        feignHeaderDTO.setClientCode(contextInfo.getClientCode());
        feignHeaderDTO.setTraceId(contextInfo.getTraceId());
        feignHeaderDTO.setServiceName(service);
        feignHeaderDTO.setApiVersion(contextInfo.getApiVersion());
        feignHeaderDTO.setRiskField(contextInfo.getRiskField());
        if (login) {
            feignHeaderDTO.setAuthorization(contextInfo.getAuthorization());
            feignHeaderDTO.setXToken(contextInfo.getAuthorization());
            feignHeaderDTO.setUserInfo(JSONUtils.toJSONString(petroMemberInfo));
        }
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            feignHeaderDTO.setInstanceId(inetAddress.getHostAddress().replaceAll("\\.", "-"));
        } catch (Exception e) {
            log.warn("获取实例ip失败");
        }
        return feignHeaderDTO;
    }
}
