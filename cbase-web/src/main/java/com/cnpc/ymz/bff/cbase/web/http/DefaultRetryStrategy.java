package com.cnpc.ymz.bff.cbase.web.http;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.ServiceUnavailableRetryStrategy;
import org.apache.http.protocol.HttpContext;

/**
 * http-client自定义重试策略
 *
 * <AUTHOR>
 * @date 2022/7/25 14:55
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public class DefaultRetryStrategy implements ServiceUnavailableRetryStrategy {

    private final int retryTimes;

    private final long retryInterval;

    public DefaultRetryStrategy(int retryTimes) {
        this.retryTimes = retryTimes;
        this.retryInterval = 1000L;
    }

    public DefaultRetryStrategy(int retryTimes, long retryInterval) {
        this.retryTimes = retryTimes;
        this.retryInterval = retryInterval;
    }

    @Override
    public boolean retryRequest(HttpResponse response, int executionCount, HttpContext context) {
        if (retryTimes <= 0) {
            return false;
        }
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_INTERNAL_SERVER_ERROR) {
            // 500错误不重试
            return false;
        }
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            // 非200错误重试
            if (executionCount <= retryTimes) {
                log.info("http请求重试,当前重试次数:{}", executionCount);
                return true;
            }
        }
        return false;
    }

    @Override
    public long getRetryInterval() {
        return retryInterval;
    }
}
