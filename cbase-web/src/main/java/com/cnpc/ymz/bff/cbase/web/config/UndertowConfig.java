package com.cnpc.ymz.bff.cbase.web.config;

import io.undertow.server.handlers.DisallowedMethodsHandler;
import io.undertow.util.HttpString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Configuration;


@Slf4j
@Configuration
public class UndertowConfig implements WebServerFactoryCustomizer<UndertowServletWebServerFactory> {
    @Override
    public void customize(UndertowServletWebServerFactory factory) {
        factory.addDeploymentInfoCustomizers(deploymentInfo -> {
            deploymentInfo.addInitialHandlerChainWrapper(handler -> {
                HttpString[] disallowedHttpMethods = {HttpString.tryFromString("TRACE"),
                        HttpString.tryFromString("TRACK")};
                return new DisallowedMethodsHandler(handler, disallowedHttpMethods);
            });
        });
    }
}
