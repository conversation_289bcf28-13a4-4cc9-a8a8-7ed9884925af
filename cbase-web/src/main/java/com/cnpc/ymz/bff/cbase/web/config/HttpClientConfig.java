package com.cnpc.ymz.bff.cbase.web.config;

import com.cnpc.ymz.bff.cbase.web.http.DefaultRetryStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;


/**
 * http配置
 * <AUTHOR>
 * @date 2025/1/15 10:20
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Configuration
public class HttpClientConfig {

    /**
     * 响应读取超时时间(单位:毫秒)
     */
    @Value("${httpRequest.readTimeout:10000}")
    private int readTimeout;

    /**
     * 建立连接超时时间(单位:毫秒)
     */
    @Value("${httpRequest.connectionTimeout:3000}")
    private int connectionTimeout;

    /**
     * 从连接池获取连接超时时间(单位:毫秒)
     */
    @Value("${httpRequest.connectionRequestTimeout:1000}")
    private int connectionRequestTimeout;

    @Bean
    @Primary
    public CloseableHttpClient getCloseableHttpClient() {
        PoolingHttpClientConnectionManager httpClientConnectionManager = new PoolingHttpClientConnectionManager();
        httpClientConnectionManager.setMaxTotal((Runtime.getRuntime().availableProcessors()) * 50);
        httpClientConnectionManager.setDefaultMaxPerRoute((Runtime.getRuntime().availableProcessors()) * 50);
        httpClientConnectionManager.setValidateAfterInactivity(2000);

        RequestConfig.Builder builder = RequestConfig.custom();
        builder.setConnectTimeout(connectionTimeout)
                .setConnectionRequestTimeout(connectionRequestTimeout)
                .setSocketTimeout(readTimeout);

        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setConnectionManager(httpClientConnectionManager);
        httpClientBuilder.setDefaultRequestConfig(builder.build());
        httpClientBuilder.evictExpiredConnections();
        httpClientBuilder.evictIdleConnections(10, TimeUnit.SECONDS);
        httpClientBuilder.setRetryHandler(new DefaultHttpRequestRetryHandler(0, false));
        httpClientBuilder.setServiceUnavailableRetryStrategy(new DefaultRetryStrategy(0));
        return httpClientBuilder.build();
    }
}
