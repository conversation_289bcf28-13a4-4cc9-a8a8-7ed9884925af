package com.cnpc.ymz.bff.cbase.web.util;


import com.cnpc.ymz.bff.cbase.web.exception.BizException;
import com.cnpc.ymz.bff.cbase.web.exception.TaskQueueFullException;
import com.cnpc.ymz.bff.cbase.web.log.LogAdapter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 多线程处理工具类
 * <AUTHOR>
 * @date 2025/5/27 10:08
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class AsyncUtils {

    @Value("${asyncUtil.get.outTime:10}")
    private long outTime;

    private ExecutorService threadPoolExecutor;

    @Value("${spring.application.name}")
    private String service;

    @Autowired
    public void setThreadPoolExecutor(ExecutorService threadPoolExecutor) {
        this.threadPoolExecutor = threadPoolExecutor;
    }

    public void runAsync(Runnable runnable) throws TaskQueueFullException {
        try {
            CompletableFuture.runAsync(() -> {
                LogAdapter.setLog();
                try {
                    runnable.run();
                } finally {
                    ContextUtils.removeContext();
                    MDC.clear();
                }
            }, threadPoolExecutor);
        } catch (RejectedExecutionException ce) {
            log.warn("{}应用线程池已满", service);
            throw new TaskQueueFullException();
        }
    }

    public <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier) throws TaskQueueFullException {
        try {
            return CompletableFuture.supplyAsync(() -> {
                LogAdapter.setLog();
                try {
                    return supplier.get();
                } finally {
                    ContextUtils.removeContext();
                    MDC.clear();
                }
            }, threadPoolExecutor);
        } catch (RejectedExecutionException ce) {
            log.warn("{}应用线程池已满", service);
            throw new TaskQueueFullException();
        }
    }

    public <T> T getResult(CompletableFuture<T> cf) {
        try {
            return cf.get(outTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            if (e.getCause() != null && e.getCause() instanceof BizException) {
                throw (BizException) e.getCause();
            } else {
                log.error("获取异步执行结果异常{}", e.getMessage(), e);
                throw new BizException("获取异步任务结果失败");
            }
        }
    }
}
