package com.cnpc.ymz.bff.cbase.web.filter;

import com.cnpc.ymz.bff.cbase.web.log.LogAdapter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 通用过滤器
 *
 * <AUTHOR>
 * @date 2023/10/20 15:00
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TraceFilter extends GenericFilterBean {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        MDC.clear();
        LogAdapter.setLog(request);
        log.info("收到访问请求:{}", ((HttpServletRequest) request).getRequestURI());
        chain.doFilter(request, response);
    }

}
