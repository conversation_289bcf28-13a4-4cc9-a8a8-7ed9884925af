package com.cnpc.ymz.bff.cbase.web.util;

import com.cnpc.ymz.bff.cbase.web.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;


/**
 * http访问工具类
 * <AUTHOR>
 * @date 2025/5/27 10:09
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Component
public class HttpUtils {

    private CloseableHttpClient closeableHttpClient;

    @Autowired
    public void setCloseableHttpClient(CloseableHttpClient closeableHttpClient) {
        this.closeableHttpClient = closeableHttpClient;
    }

    public String doGet(String url, HttpRequest httpRequest) {
        HttpEntity httpEntity = null;
        String retStr = "";
        CloseableHttpResponse response = null;
        try {
            HttpGet httpGet = new HttpGet(url);
            if (httpRequest != null) {
                for (String key : httpRequest.getHeader().keySet()) {
                    httpGet.setHeader(key, httpRequest.getHeader().get(key));
                }
            }
            long l1 = System.currentTimeMillis();
            response = closeableHttpClient.execute(httpGet);
            long l2 = System.currentTimeMillis();
            log.info("请求http:【{}】,耗时{}毫秒", httpGet.getURI(), l2 - l1);
            httpEntity = response.getEntity();
            if (httpEntity != null) {
                retStr = EntityUtils.toString(httpEntity, httpRequest == null ? "UTF-8" : httpRequest.getContentEncoding());
            }
            log.info("请求http:【{}】,返回结果:{}", httpGet.getURI(), retStr);
            return retStr;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            try {
                EntityUtils.consume(httpEntity);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public String doPost(String url, HttpRequest httpRequest) {
        HttpEntity httpEntity = null;
        String retStr = "";
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            if (httpRequest != null) {
                if (StringUtils.isNotBlank(httpRequest.getBody())) {
                    StringEntity stringEntity = new StringEntity(httpRequest.getBody(), httpRequest.getContentEncoding());
                    stringEntity.setContentEncoding(httpRequest.getContentEncoding());
                    stringEntity.setContentType(httpRequest.getContentType());
                    httpPost.setEntity(stringEntity);
                }
                for (String key : httpRequest.getHeader().keySet()) {
                    httpPost.setHeader(key, httpRequest.getHeader().get(key));
                }
                log.info("请求http:【{}】,header:{},body:{}", url, httpPost.getAllHeaders(), httpRequest.getBody());
            }
            long l1 = System.currentTimeMillis();
            response = closeableHttpClient.execute(httpPost);
            long l2 = System.currentTimeMillis();
            log.info("请求http:【{}】,耗时{}毫秒", httpPost.getURI(), l2 - l1);
            httpEntity = response.getEntity();
            if (httpEntity != null) {
                retStr = EntityUtils.toString(httpEntity, httpRequest == null ? "UTF-8" : httpRequest.getContentEncoding());
            }
            log.info("请求http:【{}】,返回结果:{}", httpPost.getURI(), retStr);
            return retStr;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            try {
                EntityUtils.consume(httpEntity);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}
