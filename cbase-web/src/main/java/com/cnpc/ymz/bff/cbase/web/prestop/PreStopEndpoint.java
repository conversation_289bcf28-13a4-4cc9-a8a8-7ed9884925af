package com.cnpc.ymz.bff.cbase.web.prestop;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结合k8s preStop hook 实现完全优雅停机
 *
 * <AUTHOR>
 * @date 2024/3/22 12:00
 * @company 昆仑数智科技有限责任公司
 */
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enable", havingValue = "true")
@ConditionalOnClass(NacosAutoServiceRegistration.class)
@RestController
@RequestMapping("actuator")
@RequiredArgsConstructor
@Slf4j
public class PreStopEndpoint {

    private final NacosAutoServiceRegistration nacosAutoServiceRegistration;

    /**
     * 注销服务后关闭应用前等待的时间(毫秒)
     */
    @Value("${preStop.waitTime:10000}")
    private int waitTime;

    @GetMapping("stopService")
    public ResponseEntity<Boolean> stopNacosService() {

        log.info("preStop-开始停止服务");
        nacosAutoServiceRegistration.stop();
        log.info("preStop-nacos反注册成功");
        log.info("preStop-等待服务依赖方收到服务下线通知,等待时间:{}", waitTime);
        try {
            Thread.sleep(waitTime);
        } catch (InterruptedException e) {
            log.info("interrupted!", e);
        }
        log.info("preStop-等待结束，可以关闭springboot容器");
        return ResponseEntity.ok(true);
    }

}