package com.cnpc.ymz.bff.cbase.web.feign;

import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.web.exception.BizException;
import com.cnpc.ymz.bff.cbase.web.inteceptor.FeignInterceptor;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import com.fasterxml.jackson.databind.JsonNode;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * feign解码器，处理非200请求
 *
 * <AUTHOR>
 * @date 2025/6/6 16:52
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public class CustomErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {

        String url = response.request() != null ? response.request().url() : "";

        // 获取feign调用耗时
        Map<String, String> map = ContextUtils.getContext().getOthers();
        Long time = null;
        if (map != null && map.containsKey(FeignInterceptor.FEIGN_TIME)) {
            long start = Long.parseLong(map.get(FeignInterceptor.FEIGN_TIME));
            time = System.currentTimeMillis() - start;
        }

        String contentType = response.headers().get("Content-Type").stream()
                .findFirst()
                .orElse("")
                .toLowerCase();
        if (!contentType.contains("application/json") || response.body() == null) {
            if (time != null) {
                log.error("Feign调用失败 - URL: {}, 耗时: {}ms,状态码: {}", url, time, response.status());
            } else {
                log.error("Feign调用失败 - URL: {}, 状态码: {}", url, response.status());
            }
            return new BizException("系统异常,请稍后再试");
        }

        // 读取响应体
        String responseBody = null;
        try {
            responseBody = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return new BizException("系统异常,请稍后再试");
        }

        if (time != null) {
            log.error("Feign调用失败 - URL: {}, 耗时: {}ms, 状态码: {}, 响应: {}", url, time, response.status(), responseBody);
        } else {
            log.error("Feign调用失败 - URL: {}, 状态码: {}, 响应: {}", url, response.status(), responseBody);
        }


        // 读取响应体的错误码
        JsonNode jsonNode = JSONUtils.parse(responseBody);
        if (jsonNode == null) {
            return new BizException("系统异常,无效的响应格式");
        }
        JsonNode eNodeCode = jsonNode.get("code");
        JsonNode eNodeMsg = jsonNode.get("msg");
        String code = (eNodeCode != null && !eNodeCode.isNull()) ? eNodeCode.asText() : null;
        String msg = (eNodeMsg != null && !eNodeMsg.isNull()) ? eNodeMsg.asText() : "系统异常,请稍后再试";
        if (StringUtils.isNotBlank(code)) {
            return new BizException(code, msg);
        } else {
            return new BizException(msg);
        }
    }
}