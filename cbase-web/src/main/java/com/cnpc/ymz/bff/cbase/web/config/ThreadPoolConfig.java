package com.cnpc.ymz.bff.cbase.web.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.*;

/**
 * 异步线程池配置
 *
 * <AUTHOR>
 * @date 2022/5/31 14:08
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Bean
    @Primary
    public ExecutorService getPool() {
        // availableProcessors在docker运行时必须运行在java8 8u191版本之上，否则取到的cpu核心数不正确
        int coreSize = Runtime.getRuntime().availableProcessors();
        int maxSize = (Runtime.getRuntime().availableProcessors()) * 5;
        int queueSize = 10;
        ThreadPoolExecutor te = new ThreadPoolExecutor(coreSize,
                maxSize, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(queueSize), r -> {
            Thread t = new Thread(r);
            t.setName("BizPool-" + t.getId());
            return t;
        }, (r, e) -> {
            // 这个异常调用方需要捕获并打印日志
            throw new RejectedExecutionException();
        });
        log.info("初始化业务线程池成功,核心线程{},最大线程{},任务队列数{}", coreSize, maxSize, queueSize);
        return TtlExecutors.getTtlExecutorService(te);
    }

}
