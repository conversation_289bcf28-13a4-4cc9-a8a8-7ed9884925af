package com.cnpc.ymz.bff.cbase.web.exception;


import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cnpc.ymz.bff.cbase.common.dto.ResponseDTO;

/**
 * 通用业务异常类
 * <AUTHOR>
 * @date 2025/7/1 13:59
 * @company 昆仑数智科技有限责任公司
 */
public class BizException extends RuntimeException {

    private final String code;

    public BizException() {
        super();
        code = "S_" + getAppCode() + "_ERROR";
    }

    public BizException(String message) {
        super(message);
        code = "S_" + getAppCode() + "_ERROR";
    }

    public BizException(ResponseDTO<?> responseDTO) {
        super(responseDTO.getMessage());
        this.code = responseDTO.getErrorCode();
    }

    public BizException(String code, String message) {
        super(message);
        this.code = code;
    }


    public BizException(String message, Throwable cause) {
        super(message, cause);
        code = "S_" + getAppCode() + "_ERROR";
    }

    public BizException(Throwable cause) {
        super(cause);
        code = "S_" + getAppCode() + "_ERROR";
    }

    private String getAppCode() {
        String appCode = SpringUtil.getApplicationContext().getEnvironment().getProperty("app.code");
        if (StrUtil.isBlank(appCode)) {
            appCode = "BFF";
        }
        return appCode;
    }

    public String getCode() {
        return code;
    }
}
