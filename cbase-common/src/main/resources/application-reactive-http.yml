# ReactiveHttp配置示例
# 可以在application.yml或application.properties中配置这些参数

reactive:
  http:
    # 连接池配置
    pool:
      # 最大连接数
      max-connections: 100
      # 获取连接超时时间(秒)
      pending-acquire-timeout: 45
      # 连接最大空闲时间(秒)
      max-idle-time: 20
      # 连接最大生存时间(分钟)
      max-life-time: 5
    
    # 超时配置
    timeout:
      # 连接超时时间(毫秒)
      connect: 5000
      # 读取超时时间(秒)
      read: 30
      # 写入超时时间(秒)
      write: 30
    
    # 重试配置
    retry:
      # 重试次数
      attempts: 3
      # 重试间隔(秒)
      backoff-seconds: 1

---
# 开发环境配置
spring:
  profiles: dev

reactive:
  http:
    pool:
      max-connections: 50
    timeout:
      connect: 3000
      read: 15
      write: 15
    retry:
      attempts: 2

---
# 生产环境配置
spring:
  profiles: prod

reactive:
  http:
    pool:
      max-connections: 200
      pending-acquire-timeout: 60
      max-idle-time: 30
      max-life-time: 10
    timeout:
      connect: 5000
      read: 60
      write: 60
    retry:
      attempts: 5
      backoff-seconds: 2
