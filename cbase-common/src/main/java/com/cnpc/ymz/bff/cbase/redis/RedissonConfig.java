package com.cnpc.ymz.bff.cbase.redis;

import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.FstCodec;
import org.redisson.codec.MarshallingCodec;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.TransportMode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * redis配置
 *
 * <AUTHOR>
 * @date 2025/1/15 10:20
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Configuration
public class RedissonConfig {

    @Value(value = "${spring.redis.cluster.nodes:0}")
    private String clusterAddress;

    @Value(value = "${spring.redis.database:0}")
    private int database;

    @Value(value = "${spring.redis.password:0}")
    private String password;

    @Value(value = "${spring.redis.host:0}")
    private String singleAddress;

    @Value(value = "${spring.redis.port:0}")
    private String singlePort;

    @Value(value = "${spring.redis.username:0}")
    private String userName;

    @Value(value = "${spring.redis.codec:0}")
    private String codec;

    @Bean
    @Primary
    public RedissonClient getRedisClient() throws Exception {
        int threads = (Runtime.getRuntime().availableProcessors()) * 2;
        int nettyThreads = (Runtime.getRuntime().availableProcessors()) * 4;
        Config config = new Config();
        config.setLockWatchdogTimeout(30000);
        config.setThreads(threads);
        config.setNettyThreads(nettyThreads);
        config.setTransportMode(TransportMode.NIO);

        if (!"0".equals(singleAddress)) {
            config.useSingleServer().setAddress("redis://" + singleAddress + ":" + singlePort)
                    .setDatabase(database)
                    .setConnectionMinimumIdleSize(5)
                    .setRetryAttempts(3)
                    .setRetryInterval(1000)
                    .setConnectTimeout(3000)
                    .setTimeout(1500)
                    .setConnectionPoolSize(128);
            if (StringUtils.isNotEmpty(password) && !password.equals("0")) {
                config.useSingleServer().setPassword(password);
            }
            if (StringUtils.isNotEmpty(userName) && !userName.equals("0")) {
                config.useSingleServer().setUsername(userName);
            }
            if (StringUtils.isNotEmpty(codec) && !codec.equals("0")) {
                if ("StringCodeC".equalsIgnoreCase(codec)) {
                    config.setCodec(new StringCodec());
                } else if ("FstCodec".equalsIgnoreCase(codec)) {
                    config.setCodec(new FstCodec());
                } else if ("MarshallingCodec".equalsIgnoreCase(codec)) {
                    config.setCodec(new MarshallingCodec());
                }
            }
            log.info("初始化redis配置成功,host:{},database:{}", singleAddress, database);
        } else if (!"0".equals(clusterAddress)) {
            String[] nodes = clusterAddress.split(",");
            for (int i = 0; i < nodes.length; i++) {
                nodes[i] = "redis://" + nodes[i];
            }
            config.useClusterServers().addNodeAddress(nodes)
                    .setRetryAttempts(3)
                    .setRetryInterval(1000)
                    .setMasterConnectionMinimumIdleSize(5)
                    .setSlaveConnectionMinimumIdleSize(5)
                    .setMasterConnectionPoolSize(128)
                    .setSlaveConnectionPoolSize(128)
                    .setReadMode(ReadMode.MASTER_SLAVE)
                    .setConnectTimeout(3000)
                    .setTimeout(1500);
            if (StringUtils.isNotEmpty(password) && !password.equals("0")) {
                config.useClusterServers().setPassword(password);
            }
            if (StringUtils.isNotEmpty(codec) && !codec.equals("0")) {
                if ("StringCodeC".equalsIgnoreCase(codec)) {
                    config.setCodec(new StringCodec());
                } else if ("FstCodec".equalsIgnoreCase(codec)) {
                    config.setCodec(new FstCodec());
                } else if ("MarshallingCodec".equalsIgnoreCase(codec)) {
                    config.setCodec(new MarshallingCodec());
                }
            }
            log.info("初始化redis-cluster配置成功,host:{}", clusterAddress);
        }
        return Redisson.create(config);
    }
}
