package com.cnpc.ymz.bff.cbase.common.dto;

import lombok.Data;

/**
 * 前端通用返回对象
 *
 * <AUTHOR>
 * @date 2023/10/20 14:55
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class ResponseDTO<T> {

    private boolean success;

    private T data;

    private String message;

    private String errorCode;

    public static <T> ResponseDTO<T> success(T data) {
        ResponseDTO<T> responseDTO = new ResponseDTO<>();
        responseDTO.setData(data);
        responseDTO.setSuccess(true);
        responseDTO.setMessage("请求成功");
        return responseDTO;
    }

    public static <T> ResponseDTO<T> success(T data, String message) {
        ResponseDTO<T> responseDTO = new ResponseDTO<>();
        responseDTO.setData(data);
        responseDTO.setSuccess(true);
        responseDTO.setMessage(message);
        return responseDTO;
    }

    public static ResponseDTO<Void> fail(String errorCode, String message) {
        ResponseDTO<Void> responseDTO = new ResponseDTO<>();
        responseDTO.setSuccess(false);
        responseDTO.setErrorCode(errorCode);
        responseDTO.setMessage(message);
        responseDTO.setData(null);
        return responseDTO;
    }
}
