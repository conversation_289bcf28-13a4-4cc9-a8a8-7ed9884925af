package com.cnpc.ymz.bff.cbase.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * 信息摘要处理工具类
 * <AUTHOR>
 * @date 2023/10/20 14:58
 * @company 昆仑数智科技有限责任公司
 */
public class SHA256Utils {

    public static String getSHA256String(String str) {
        String encodeStr = "";
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (Exception e) {
        	throw new RuntimeException("SHA摘要错误", e);
        }
        return encodeStr;
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        for (byte aByte : bytes) {
            String temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    /*public static void main(String[] args) {
        String str = "{\"orderId\":2,\"str\":\"么么哒么么么热热了偷偷摸摸有空哦绒儿童款让他看他那天\"}";
        System.out.println("信息摘要:" + SHA256Utils.getSHA256String(str));
    }*/
}
