package com.cnpc.ymz.bff.cbase.redis;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 自定义redis缓存注解
 *
 * <AUTHOR>
 * @date 2022/6/27 11:38
 * @company 昆仑数智科技有限责任公司
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface QueryCache {
    String key();

    String ignoreCacheFiledName() default ""; // 忽略缓存，强制查询数据源的字段名称,从接口获取

    String ignoreCacheFiledValue() default ""; // 忽略缓存，强制查询数据源的字段值,从接口获取

    String switchOn() default ""; // 缓存开关配置项,从属性文件获取

    boolean cacheNull() default false; // 是否缓存空值 避免缓存穿透

    long expireTime() default 0L;  // 超时时间

    TimeUnit timeUnit() default TimeUnit.MILLISECONDS;
}
