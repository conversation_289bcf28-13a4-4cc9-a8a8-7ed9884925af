package com.cnpc.ymz.bff.cbase.common.utils;

import org.apache.commons.codec.binary.Base64;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

public class DESUtils {

    // 修改为使用CBC模式
    private static final String CIPHER_TRANSFORMAT = "TripleDES/CBC/PKCS5Padding";
    private static final String ALGORITHM = "TripleDES";
    private static final String ENCODING = "GB2312";
    private static final int IV_LENGTH = 8; // 3DES的IV长度是8字节

    private static final char[] HEX_CHAR = {'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    private static final Integer ENCRYPT_TYPE = 0;

    /*public static void main(String[] args) {
        try {
            String str = encryptDES("{\"orderId\":2,\"str\":\"哈哈\"}", "oommmkkjeennqw99732!!@#$##");
            // 加密
            System.out.println("加密后密文:" + str);

            // 解密
            System.out.println("解密后字符串：" + decryptFromDES(str, "oommmkkjeennqw99732!!@#$##"));

        } catch (Exception e) {
        	throw new RuntimeException("算法错误", e);
        }
    }*/

    /**
     * 生成随机的初始化向量
     */
    private static byte[] generateIv() {
        byte[] iv = new byte[IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    /**
     * 获取字符串3DES加密结果，密钥先用MD5加密
     */
    public static String encryptToBase64(String plainText, String key) throws Exception {
        byte[] ivBytes = generateIv();
        SecretKey md5Key = new SecretKeySpec(keyMD5Encode(key), ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMAT);
        cipher.init(Cipher.ENCRYPT_MODE, md5Key, new IvParameterSpec(ivBytes));
        byte[] encrypted = cipher.doFinal(plainText.getBytes(ENCODING));

        // 将IV和加密数据一起返回
        byte[] combined = new byte[ivBytes.length + encrypted.length];
        System.arraycopy(ivBytes, 0, combined, 0, ivBytes.length);
        System.arraycopy(encrypted, 0, combined, ivBytes.length, encrypted.length);

        return Base64.encodeBase64String(combined);
    }

    /**
     * 获取字符串3DES加密结果，密钥先用MD5加密
     */
    public static String encryptDES(String plainText, String enckey) throws Exception {
        return encryptToBase64(plainText, enckey);
    }

    /**
     * 2次MD5加密
     */
    public static String encryptBy2Md5(String plainText, String key) throws Exception {
        if (0 == ENCRYPT_TYPE) {
            return encryptToBase64(plainText, key);
        }
        MessageDigest md5 = null;
        String newstr = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            String firstString = bytesToHexFun2(md5.digest(plainText.getBytes(StandardCharsets.UTF_8)));
            BASE64Encoder base64en = new BASE64Encoder();
            newstr = base64en.encode(md5.digest(firstString.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("算法错误", e);
        }
        return newstr;
    }

    /**
     * 将3DES加密结果的base64字符串进行解密
     */
    public static String decryptFromBase64String(String plainText, String key) throws Exception {
        byte[] combined = Base64.decodeBase64(plainText.getBytes());
        byte[] ivBytes = new byte[IV_LENGTH];
        byte[] encrypted = new byte[combined.length - IV_LENGTH];

        System.arraycopy(combined, 0, ivBytes, 0, IV_LENGTH);
        System.arraycopy(combined, IV_LENGTH, encrypted, 0, encrypted.length);

        SecretKey md5Key = new SecretKeySpec(keyMD5Encode(key), ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMAT);
        cipher.init(Cipher.DECRYPT_MODE, md5Key, new IvParameterSpec(ivBytes));
        byte[] result = cipher.doFinal(encrypted);
        return new String(result, ENCODING);
    }

    public static String decryptFromDES(String plainText, String key) throws Exception {
        return decryptFromBase64String(plainText, key);
    }

    /**
     * 密钥先进行MD5处理，因为java中3DES必须要求key为24位，所以要补齐24位
     */
    public static byte[] keyMD5Encode(String key) throws Exception {
        byte[] btInput = key.getBytes(ENCODING);
        MessageDigest mdInst = MessageDigest.getInstance("MD5");
        mdInst.update(btInput);
        byte[] md = mdInst.digest();

        byte[] outputKey = new byte[24];
        try {
            System.arraycopy(md, 0, outputKey, 0, 16);
            System.arraycopy(md, 0, outputKey, 16, 8);
        } catch (Exception e) {
            throw new RuntimeException("16秘钥转24秘钥异常");
        }
        return outputKey;
    }

    public static byte[] keyMD5EncodeNew(String key) throws Exception {
        byte[] btInput = key.getBytes(ENCODING);
        MessageDigest mdInst = MessageDigest.getInstance("MD5");
        mdInst.update(btInput);
        byte[] md = mdInst.digest();

        byte[] outputKey = new byte[24];
        try {
            System.arraycopy(md, 0, outputKey, 0, 16);
        } catch (Exception e) {
            throw new RuntimeException("16秘钥转24秘钥异常");
        }
        return outputKey;
    }

    private static String bytesToHexFun2(byte[] bytes) {
        char[] buf = new char[bytes.length * 2];
        int index = 0;
        for (byte b : bytes) {
            buf[index++] = HEX_CHAR[b >>> 4 & 0xf];
            buf[index++] = HEX_CHAR[b & 0xf];
        }
        return new String(buf);
    }
}