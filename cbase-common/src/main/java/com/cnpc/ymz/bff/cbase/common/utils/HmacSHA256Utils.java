package com.cnpc.ymz.bff.cbase.common.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * HMACSHA256加密
 */
public class HmacSHA256Utils {

    public static String getHMACSHA256String(final String strText, final String strKey) {
        String strResult = null;
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(strKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secretKey);
            byte[] hash = sha256_HMAC.doFinal(strText.getBytes(StandardCharsets.UTF_8));
            strResult = toHexString(hash);
            return strResult;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            return strResult;
        }
    }

    private static String toHexString(byte[] b){
        StringBuilder hexString = new StringBuilder();
        String stmp;
        for(int n = 0; b != null && n < b.length; n++){
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hexString.append('0');
            hexString.append(stmp);
        }
        return hexString.toString();
    }


    /*public static void main(String[] args) {
        String str = "api_version=1.0&appid=wx368676e079814986&credential_type=MAINLAND_ID&mch_id=1226183401&nonce_str=" +
                "768378501181270612&openid=o4f4N5kYxkW0jVjdpOyTtZ_Pslq4&response_type=code&scope=pay_realname&sign_type=" +
                "HMAC-SHA256&key=CHzoP8tmxLA7sC031qBUMP2AOHkJV8KO";
        String key = "CHzoP8tmxLA7sC031qBUMP2AOHkJV8KO";
        System.out.println(HMACSHA256Utils.getHMACSHA256String(str, key).toUpperCase());
        System.out.println(Integer.toHexString((byte)-255 & 0XFF));
    }*/

}
