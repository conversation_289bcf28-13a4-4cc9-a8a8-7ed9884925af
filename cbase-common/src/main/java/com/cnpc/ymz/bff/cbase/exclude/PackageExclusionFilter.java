package com.cnpc.ymz.bff.cbase.exclude;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigurationImportFilter;
import org.springframework.boot.autoconfigure.AutoConfigurationMetadata;

import java.util.Arrays;
import java.util.List;

/**
 * 按包名排除自动加载的不需要的类
 * <AUTHOR>
 * @date 2025/4/24 08:31
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public class PackageExclusionFilter implements AutoConfigurationImportFilter {

    private static final List<String> EXCLUDED_PACKAGES = Arrays.asList(
            "com.kld.foundation.utils"
    );

    @Override
    public boolean[] match(String[] autoConfigurationClasses, AutoConfigurationMetadata metadata) {
        if (autoConfigurationClasses == null) {
            return new boolean[0];
        }

        boolean[] matches = new boolean[autoConfigurationClasses.length];
        for (int i = 0; i < autoConfigurationClasses.length; i++) {
            String className = autoConfigurationClasses[i];
            boolean shouldInclude = shouldInclude(className);
            if (!shouldInclude) {
                log.info("Excluding auto-configuration class: {}", className);
            }
            matches[i] = shouldInclude;
        }
        return matches;
    }

    private boolean shouldInclude(String className) {
        if (className == null) {
            return true;
        }
        return !EXCLUDED_PACKAGES.stream().anyMatch(className::startsWith);
    }
}