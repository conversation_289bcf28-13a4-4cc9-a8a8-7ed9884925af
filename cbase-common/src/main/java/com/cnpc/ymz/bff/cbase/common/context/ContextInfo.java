package com.cnpc.ymz.bff.cbase.common.context;

import java.util.HashMap;
import java.util.Map;

/**
 * 线程上下文对象
 *
 * <AUTHOR>
 * @date 2023/10/20 14:59
 * @company 昆仑数智科技有限责任公司
 */
public class ContextInfo {
    // 渠道号
    private String clientCode;

    // 业务渠道号
    private String sourceCode;

    // 客户端ip地址
    private String ip;

    // 链路id
    private String traceId;

    // 用户编号
    private String userId;

    // 设备名称
    private String deviceName;

    // 设备唯一编号
    private String deviceId;

    // 设备型号
    private String deviceNo;

    // 用户操作时间戳
    private String time;

    // 接口版本号
    private String apiVersion;

    // 风控信息
    private String riskField;

    // token
    private String authorization;

    // 用户姓名
    private String userName;

    // 自定义上下文字段,需要通过自定义拦截器去设置值
    private Map<String, String> others;

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String getRiskField() {
        return riskField;
    }

    public void setRiskField(String riskField) {
        this.riskField = riskField;
    }

    public String getAuthorization() {
        return authorization;
    }

    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Map<String, String> getOthers() {
        if (others == null) {
            others = new HashMap<>();
        }
        return others;
    }

    public void setOthers(Map<String, String> others) {
        this.others = others;
    }
}
