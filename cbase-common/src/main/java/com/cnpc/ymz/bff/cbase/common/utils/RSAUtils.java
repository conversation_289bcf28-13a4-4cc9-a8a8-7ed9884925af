package com.cnpc.ymz.bff.cbase.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RSAUtils {
    public static final String CHARSET = "UTF-8";
    public static final String RSA_ALGORITHM = "RSA";
    public static final String RSA_ENCRYPTION_ALGORITHM = "RSA";
    public static final String RSA_SIGNATURE_ALGORITHM = "SHA256withRSA";

    /*public static void main(String[] args) throws Exception {
        Map<String, String> keyMap = RSAUtils.createKeys(2048);
        String gy = keyMap.get("publicKey");
        String sy = keyMap.get("privateKey");
        System.out.println("公钥: \n\r" + gy);
        System.out.println("私钥： \n\r" + sy);

        System.out.println("=================================");

        // 签名和验证
        String dataToSign = "{\"valid\":\"4\",\"scene\":\"4\",\"authInfo\":\"13042991046\",\"time\":1747360373825}\n";
        String signature = sign(dataToSign, getPrivateKey(sy));
        System.out.println("签名:" + signature);
        System.out.println("验证签名:" + verify(dataToSign, signature, getPublicKey(gy)));

        // 公钥加密私钥解密
        String s1 = publicEncrypt(dataToSign, getPublicKey(gy));
        System.out.println("公钥加密:" + s1);
        System.out.println("私钥解密:" + privateDecrypt(s1, getPrivateKey(sy)));

        // 私钥加密公钥解密
        String s = RSAUtils.privateEncrypt(dataToSign, RSAUtils.getPrivateKey(sy));
        System.out.println("私钥加密:" + s);
        System.out.println("公钥解密" + RSAUtils.publicDecrypt(s, RSAUtils.getPublicKey(gy)));

        // 测试能加密的最大长度
        *//*StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            sb.append("a");
            try {
                publicEncrypt(sb.toString(), getPublicKey(gy));
            } catch (Exception e) {
                System.out.println("可加密的最大字符长度:" + i);
                break;
            }
        }*//*
        System.out.println("测试结束");
    }*/

    // 创建密钥对方法保持不变
    public static Map<String, String> createKeys(int keySize) {
        KeyPairGenerator kpg;
        try {
            kpg = KeyPairGenerator.getInstance(RSA_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("No such algorithm-->[" + RSA_ALGORITHM + "]");
        }
        kpg.initialize(keySize);
        KeyPair keyPair = kpg.generateKeyPair();
        Key publicKey = keyPair.getPublic();
        String publicKeyStr = Base64.encodeBase64URLSafeString(publicKey.getEncoded());
        Key privateKey = keyPair.getPrivate();
        String privateKeyStr = Base64.encodeBase64URLSafeString(privateKey.getEncoded());
        Map<String, String> keyPairMap = new HashMap<>();
        keyPairMap.put("publicKey", publicKeyStr);
        keyPairMap.put("privateKey", privateKeyStr);
        return keyPairMap;
    }

    // 获取公钥私钥方法保持不变
    public static RSAPublicKey getPublicKey(String publicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
        return (RSAPublicKey) keyFactory.generatePublic(x509KeySpec);
    }

    public static RSAPrivateKey getPrivateKey(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
        return (RSAPrivateKey) keyFactory.generatePrivate(pkcs8KeySpec);
    }

    // 加密方法使用OAEP填充
    public static String publicEncrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ENCRYPTION_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            return Base64.encodeBase64URLSafeString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(CHARSET), publicKey.getModulus().bitLength()));
        } catch (Exception e) {
            throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
        }
    }

    public static String privateDecrypt(String data, RSAPrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ENCRYPTION_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, Base64.decodeBase64(data), privateKey.getModulus().bitLength()), CHARSET);
        } catch (Exception e) {
            throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
        }
    }

    // 签名方法
    public static String sign(String data, RSAPrivateKey privateKey) {
        try {
            Signature signature = Signature.getInstance(RSA_SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(data.getBytes(CHARSET));
            return Base64.encodeBase64URLSafeString(signature.sign());
        } catch (Exception e) {
            throw new RuntimeException("签名字符串[" + data + "]时遇到异常", e);
        }
    }

    // 验证签名方法
    public static boolean verify(String data, String sign, RSAPublicKey publicKey) {
        try {
            Signature signature = Signature.getInstance(RSA_SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data.getBytes(CHARSET));
            return signature.verify(Base64.decodeBase64(sign));
        } catch (Exception e) {
            throw new RuntimeException("验证签名[" + sign + "]时遇到异常", e);
        }
    }

    @Deprecated
    public static String privateEncrypt(String data, RSAPrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            return Base64.encodeBase64URLSafeString(
                    rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE,
                            data.getBytes(CHARSET),
                            privateKey.getModulus().bitLength()
                    ));
        } catch (Exception e) {
            throw new RuntimeException("私钥加密字符串[" + data + "]时遇到异常", e);
        }
    }

    @Deprecated
    public static String publicDecrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            return new String(
                    rsaSplitCodec(cipher, Cipher.DECRYPT_MODE,
                            Base64.decodeBase64(data),
                            publicKey.getModulus().bitLength()),
                    CHARSET
            );
        } catch (Exception e) {
            throw new RuntimeException("公钥解密字符串[" + data + "]时遇到异常", e);
        }
    }

    private static byte[] rsaSplitCodec(Cipher cipher, int opmode, byte[] datas, int keySize) {
        int maxBlock;
        if (opmode == Cipher.DECRYPT_MODE) {
            maxBlock = keySize / 8;
        } else {
            // 根据填充方式计算最大块大小
            String algorithm = cipher.getAlgorithm();
            if (algorithm.contains("OAEP")) {
                // OAEPWithSHA-256AndMGF1Padding 需要更多空间
                maxBlock = keySize / 8 - 42;
            } else {
                // PKCS1Padding
                maxBlock = keySize / 8 - 11;
            }
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] buff;
        int i = 0;
        try {
            while (datas.length > offSet) {
                if (datas.length - offSet > maxBlock) {
                    buff = cipher.doFinal(datas, offSet, maxBlock);
                } else {
                    buff = cipher.doFinal(datas, offSet, datas.length - offSet);
                }
                out.write(buff, 0, buff.length);
                i++;
                offSet = i * maxBlock;
            }
        } catch (Exception e) {
            throw new RuntimeException("加解密阀值为[" + maxBlock + "]的数据时发生异常", e);
        }
        byte[] resultDatas = out.toByteArray();
        IOUtils.closeQuietly(out);
        return resultDatas;
    }
}