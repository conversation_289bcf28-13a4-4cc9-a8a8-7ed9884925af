package com.cnpc.ymz.bff.cbase.common.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

public class AESUtils {

    private static final String ALGORITHM = "AES";
    // 修改为使用CBC模式
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final int IV_LENGTH = 16; // AES的IV长度是16字节

    /*public static void main(String[] args) throws Exception {

        // 测试密钥（必须是16/24/32字节长度）
        String key128 = "ThisIsA128BitKey"; // 16字节
        String key192 = "ThisIsA192BitKey12345678"; // 24字节
        String key256 = "ThisIsA256BitKey1234567890123456"; // 32字节

        // 测试明文
        String originalText = "这是一段需要加密的敏感数据，包含中文和English混合内容！@#$%^&*()_+1234567890";

        System.out.println("============= 加密测试 =============");
        System.out.println("原始文本: " + originalText);
        System.out.println("密钥: " + key192);

        // 加密
        String encryptedText = AESUtils.encrypt(originalText, key192);
        System.out.println("加密结果: " + encryptedText);

        // 测试相同输入多次加密结果不同（因为使用了随机IV）
        String encryptedText2 = AESUtils.encrypt(originalText, key192);
        System.out.println("第二次加密结果: " + encryptedText2);
        System.out.println("两次加密结果是否相同: " + encryptedText.equals(encryptedText2));

        System.out.println("\n============= 解密测试 =============");

        // 解密第一次加密结果
        String decryptedText = AESUtils.decrypt(encryptedText, key192);
        System.out.println("解密结果: " + decryptedText);
        System.out.println("解密是否成功: " + originalText.equals(decryptedText));

        // 解密第二次加密结果
        String decryptedText2 = AESUtils.decrypt(encryptedText2, key192);
        System.out.println("第二次解密结果: " + decryptedText2);
        System.out.println("解密是否成功: " + originalText.equals(decryptedText2));

    }*/

    /**
     * AES加密字符串
     *
     * @param message 待加密的字符串
     * @param key     密钥（必须是16、24或32字节长度）
     * @return base64 密文（包含IV和加密数据）
     * @throws Exception
     */
    public static String encrypt(String message, String key) throws Exception {
        if (message == null) {
            return null;
        }

        // 生成随机IV
        byte[] iv = new byte[IV_LENGTH];
        new SecureRandom().nextBytes(iv);

        // AES专用密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);

        // 创建密码器并初始化
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv));

        // 加密数据
        byte[] encrypted = cipher.doFinal(message.getBytes(StandardCharsets.UTF_8));

        // 将IV和加密数据合并后返回base64编码
        byte[] combined = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);

        return Base64.getEncoder().encodeToString(combined);
    }

    /**
     * 解密AES加密过的字符串
     *
     * @param encryptMessage 带解密的字符串（包含IV和加密数据）
     * @param key            密钥（必须是16、24或32字节长度）
     * @return 解密后的字符串
     * @throws Exception
     */
    public static String decrypt(String encryptMessage, String key) throws Exception {
        if (encryptMessage == null) {
            return null;
        }

        // 解码base64
        byte[] combined = Base64.getDecoder().decode(encryptMessage);

        // 提取IV（前16字节）
        byte[] iv = new byte[IV_LENGTH];
        System.arraycopy(combined, 0, iv, 0, iv.length);

        // 提取加密数据（剩余字节）
        byte[] encrypted = new byte[combined.length - IV_LENGTH];
        System.arraycopy(combined, IV_LENGTH, encrypted, 0, encrypted.length);

        // AES专用密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);

        // 创建密码器并初始化
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));

        // 解密数据
        byte[] decrypted = cipher.doFinal(encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}