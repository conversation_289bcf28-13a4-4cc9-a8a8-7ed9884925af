package com.cnpc.ymz.bff.cbase.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.net.URLEncoder;

public class HeaderUtils {

    public static String decode(String s) {
        try {
            if (StringUtils.isNotBlank(s)) {
                s = URLDecoder.decode(s, "UTF-8");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return s;
    }

    public static String encode(String s) {
        try {
            if (StringUtils.isNotBlank(s)) {
                s = URLEncoder.encode(s, "UTF-8");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return s;
    }
}
