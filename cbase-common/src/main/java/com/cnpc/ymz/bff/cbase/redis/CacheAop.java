package com.cnpc.ymz.bff.cbase.redis;

import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * redis缓存切面类
 *
 * <AUTHOR>
 * @date 2022/6/27 13:05
 * @company 昆仑数智科技有限责任公司
 */
@Aspect
@Component
@Slf4j
public class CacheAop {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private Environment environment;

    @Pointcut("@annotation(com.cnpc.ymz.bff.cbase.redis.QueryCache)")
    public void query() {
    }

    @Pointcut("@annotation(com.cnpc.ymz.bff.cbase.redis.DeleteCache)")
    public void delete() {
    }

    @Around("query()")
    public Object interceptorQuery(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = methodSignature.getMethod();
        QueryCache queryCache = targetMethod.getAnnotation(QueryCache.class);
        return handleQueryCache(joinPoint, targetMethod, queryCache);
    }

    @Around("delete()")
    public Object interceptorDelete(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = methodSignature.getMethod();
        DeleteCache deleteCache = targetMethod.getAnnotation(DeleteCache.class);
        return handleDeleteCache(joinPoint, deleteCache);
    }

    private Object handleDeleteCache(ProceedingJoinPoint joinPoint, DeleteCache deleteCache) throws Throwable {
        Map<String, Object> map = getNameAndValue(joinPoint);
        Object object = null;
        Throwable throwable = null;
        try {
            object = joinPoint.proceed();
        } catch (Throwable te) {
            if (!deleteCache.alwaysDelete()) {
                throw te;
            }
            throwable = te;
        }

        String ids = deleteCache.key();
        for (String id : ids.split("##")) {
            try {
                id = parseId(id, object, map);
                redisUtils.delete(id);
            } catch (Exception e) {
                log.error("删除缓存失败", e);
            }
        }
        if (throwable != null) {
            throw throwable;
        }
        return object;
    }

    private Object handleQueryCache(ProceedingJoinPoint joinPoint, Method targetMethod, QueryCache queryCache) throws Throwable {
        String id = queryCache.key();
        String switchOn = queryCache.switchOn();

        if (StringUtils.isNotBlank(switchOn)) {
            String switchValue = environment.getProperty(switchOn);
            if (StringUtils.isNotBlank(switchValue) && "turn-off".equalsIgnoreCase(switchValue)) {
                log.info("缓存开关已关闭，强制从数据源获取数据");
                return joinPoint.proceed();
            }
        }

        Map<String, Object> map = getNameAndValue(joinPoint);
        try {
            id = parseId(id, null, map);
        } catch (Exception e) {
            log.error("缓存格式错误{}", id, e);
            return joinPoint.proceed();
        }

        if (ignoreCache(queryCache, map)) {
            log.info("忽略缓存key={},强制从数据源查询", id);
            return joinPoint.proceed();
        }

        String redisResult = getStringFromRedis(id);

        return parseResult(targetMethod, joinPoint, queryCache, id, redisResult);
    }

    private Object parseResult(Method method, ProceedingJoinPoint joinPoint,
                               QueryCache queryCache,
                               String id, String redisResult) throws Throwable {
        if (redisResult != null) {
            // 缓存不为空，直接取缓存的值返回
            // 特殊情况是缓存了空值
            if ("".equals(redisResult)) {
                return null;
            } else {
                try {
                    return JSONUtils.parseObject(redisResult, method.getReturnType());
                } catch (RuntimeException re) {
                    log.error("反序列化错误,忽略缓存数据", re);
                    return joinPoint.proceed();
                }
            }
        } else {
            // 缓存为空,从数据库获取,并更新到缓存
            // 可以缓存空值
            Object result = joinPoint.proceed();
            String cacheResult = "";
            if (result != null) {
                cacheResult = JSONUtils.toJSONString(result);
            }
            if (isNull(cacheResult) && !queryCache.cacheNull()) {
                return result;
            }

            if (queryCache.expireTime() > 0L) {
                redisUtils.set(id, cacheResult, queryCache.expireTime(), queryCache.timeUnit());
            } else {
                redisUtils.set(id, cacheResult);
            }
            return result;
        }
    }

    private boolean isNull(String str) {
        return StringUtils.isBlank(str) || "[]".equals(str) || "{}".equals(str);
    }

    private String getStringFromRedis(String id) {
        String redisResult = null;
        if (StringUtils.isNotBlank(id)) {
            redisResult = redisUtils.get(id);
        } else {
            throw new RuntimeException("cache use wrong");
        }
        return redisResult;
    }

    private boolean ignoreCache(QueryCache queryCache, Map<String, Object> map) {
        if (StringUtils.isBlank(queryCache.ignoreCacheFiledName())
                || StringUtils.isBlank(queryCache.ignoreCacheFiledValue())) {
            return false;
        }
        String id = queryCache.ignoreCacheFiledName();
        String value = queryCache.ignoreCacheFiledValue();
        try {
            if (id.contains("$")) {
                String s = id.substring(id.indexOf("{") + 1, id.indexOf("}"));
                String result = "";
                if (s.contains("request")) {
                    String[] args = s.split("\\.");
                    if (args.length == 2) {
                        result = String.valueOf(map.get(args[1]));
                    } else if (args.length > 2) {
                        Object o = map.get(args[1]);
                        for (int i = 2; i <= args.length - 1; i++) {
                            String methodName = getMethodName(args[i]);
                            o = o.getClass().getMethod(methodName).invoke(o);
                        }
                        result = String.valueOf(o);
                    }
                } else {
                    throw new Exception("use cache error");
                }
                return result.equals(value);
            }
        } catch (Exception e) {
            log.error("解析ignoreCache异常", e);
        }
        return false;
    }

    private String parseId(String id, Object object, Map<String, Object> map) throws Exception {
        while (id.contains("$")) {
            String s = id.substring(id.indexOf("{") + 1, id.indexOf("}"));
            String s1 = "${" + s + "}";
            String result = "";
            if (s.contains("request")) {
                String[] args = s.split("\\.");
                if (args.length == 2) {
                    result = String.valueOf(map.get(args[1]));
                } else if (args.length > 2) {
                    Object o = map.get(args[1]);
                    for (int i = 2; i <= args.length - 1; i++) {
                        String methodName = getMethodName(args[i]);
                        o = o.getClass().getMethod(methodName).invoke(o);
                    }
                    result = String.valueOf(o);
                }
            } else if (s.contains("response")) {
                String[] args = s.split("\\.");
                if (args.length == 2) {
                    String methodName = getMethodName(args[1]);
                    result = String.valueOf(object.getClass().getMethod(methodName).invoke(object));
                } else if (args.length > 2) {
                    Object o = object.getClass().getMethod(getMethodName(args[1])).invoke(object);
                    for (int i = 2; i <= args.length - 1; i++) {
                        String methodName = getMethodName(args[i]);
                        o = o.getClass().getMethod(methodName).invoke(o);
                    }
                    result = String.valueOf(o);
                }
            } else {
                throw new Exception("use cache error");
            }
            id = id.replace(s1, result);
        }
        return id;
    }

    private String getMethodName(String s) {
        return "get" + Character.toUpperCase(s.charAt(0)) + s.substring(1);
    }

    Map<String, Object> getNameAndValue(ProceedingJoinPoint joinPoint) {
        Map<String, Object> param = new HashMap<>();
        Object[] paramValues = joinPoint.getArgs();
        String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
        for (int i = 0; i < paramNames.length; i++) {
            param.put(paramNames[i], paramValues[i]);
        }
        return param;
    }

}
