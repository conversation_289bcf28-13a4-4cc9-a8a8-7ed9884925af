package com.cnpc.ymz.bff.cbase.rocketmq.log;

import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.rocketmq.dto.MQBaseDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;


public class LogAdapter {

    /**
     * 设置日志LOG字段信息
     */
    public static void setLog(MQBaseDTO message) {
        StringBuilder logInfo = new StringBuilder();
        String userId = message.getContextInfo().getUserId();
        if (StringUtils.isBlank(userId)) {
            userId = "";
        }
        logInfo.append(userId).append("|");
        String traceId = message.getContextInfo().getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = "";
        }
        logInfo.append(traceId).append("|");
        String clientCode = message.getContextInfo().getClientCode();
        if (StringUtils.isBlank(clientCode)) {
            clientCode = "";
        }
        logInfo.append(clientCode);
        MDC.put("LOG", logInfo.toString());
    }

    /**
     * 设置日志LOG字段信息
     */
    public static void setLog(ContextInfo contextInfo) {
        StringBuilder logInfo = new StringBuilder();
        String userId = contextInfo.getUserId();
        if (StringUtils.isBlank(userId)) {
            userId = "";
        }
        logInfo.append(userId).append("|");
        String traceId = contextInfo.getTraceId();
        if (StringUtils.isBlank(traceId)) {
            traceId = "";
        }
        logInfo.append(traceId).append("|");
        String clientCode = contextInfo.getClientCode();
        if (StringUtils.isBlank(clientCode)) {
            clientCode = "";
        }
        logInfo.append(clientCode);
        MDC.put("LOG", logInfo.toString());
    }
}
