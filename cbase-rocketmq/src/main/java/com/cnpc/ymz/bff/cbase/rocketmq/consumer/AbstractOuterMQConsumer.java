package com.cnpc.ymz.bff.cbase.rocketmq.consumer;


import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.rocketmq.log.LogAdapter;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.MDC;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 消费外部应用域的消息
 * 子类需要标注@RocketMQMessageListener注解
 *
 * <AUTHOR>
 * @date 2025/1/22 11:49
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public abstract class AbstractOuterMQConsumer<T> implements RocketMQListener<String> {

    private final Class<T> messageType;

    @SuppressWarnings("unchecked")
    public AbstractOuterMQConsumer() {
        Type superClass = getClass().getGenericSuperclass();
        while (superClass != null && !(superClass instanceof ParameterizedType)) {
            superClass = ((Class<?>) superClass).getGenericSuperclass();
        }
        if (superClass == null) {
            throw new IllegalArgumentException("必须指定具体泛型类型");
        }
        this.messageType = (Class<T>) ((ParameterizedType) superClass).getActualTypeArguments()[0];
    }

    protected abstract void doConsume(Object message);

    protected abstract ContextInfo setContext(Object message);

    @Override
    public void onMessage(String message) {
        Object messageDTO = JSONUtils.parseObject(message, messageType);
        if (messageDTO == null) {
            log.error("消息解析失败,消息体:{}", message);
            return;
        }
        ContextInfo contextInfo = setContext(messageDTO);
        ContextUtils.setContext(contextInfo); // 设置上下文
        LogAdapter.setLog(contextInfo); // 设置日志
        log.info("收到mq消息:{}", message);
        try {
            doConsume(messageDTO);
        } catch (Exception e) {
            log.error("消息消费失败", e);
            throw e;
        } finally {
            MDC.clear();
        }
    }
}
