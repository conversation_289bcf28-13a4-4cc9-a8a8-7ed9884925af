package com.cnpc.ymz.bff.cbase.rocketmq.producer;


import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.rocketmq.dto.MQBaseDTO;
import com.cnpc.ymz.bff.cbase.rocketmq.enums.DelayLevelEnums;
import com.cnpc.ymz.bff.cbase.rocketmq.exception.MQSendFailedException;
import com.cnpc.ymz.bff.cbase.web.exception.BizException;
import com.cnpc.ymz.bff.cbase.web.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.UUID;

/**
 * rocket-mq发送消息通用类
 *
 * <AUTHOR>
 * @date 2025/1/21 16:31
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class RocketMqProducer {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producer.send-message-timeout:3000}")
    private long syncTimeout;

    /**
     * 发送同步tag消息
     *
     * <AUTHOR>
     * @date 2025/1/21 16:32
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessage(String topic, String tag, Object message) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .setHeader("TAGS", tag)
                .build();
        long s = System.currentTimeMillis();
        try {
            SendResult sendResult = rocketMQTemplate.syncSend(topic, msg, syncTimeout);
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("sendResult:{}", sendResult);
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("向topic:{}发送同步消息失败,tag:{}", topic, tag);
            throw new MQSendFailedException();
        }
        long e = System.currentTimeMillis();
        log.info("向topic:{}发送同步消息成功,耗时:{}ms,tag:{},message:{}",
                topic, e - s, tag, msgStr);
    }

    private void setContext(Object message) {
        if (message instanceof MQBaseDTO) {
            try {
                Method method = MQBaseDTO.class.getMethod("setContextInfo", ContextInfo.class);
                method.invoke(message, ContextUtils.getContext());
            } catch (Exception e) {
                log.error("反射设置上下文信息失败", e);
                throw new BizException();
            }
        }
    }

    /**
     * 发送异步消息
     *
     * <AUTHOR>
     * @date 2025/1/22 09:54
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessageAsync(String topic, String tag, Object message, SendCallback sendCallback) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .setHeader("TAGS", tag)
                .build();
        try {
            rocketMQTemplate.asyncSend(topic, msg, sendCallback, syncTimeout);
        } catch (Exception e) {
            log.error("向topic:{}发送异步消息失败,tag:{}", topic, tag);
            throw new MQSendFailedException();
        }
        log.info("向topic:{}提交异步消息,tag:{},message:{}",
                topic, tag, msgStr);
    }

    /**
     * 发送异步消息
     *
     * <AUTHOR>
     * @date 2025/1/22 09:54
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessageAsync(String topic, Object message, SendCallback sendCallback) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .build();
        try {
            rocketMQTemplate.asyncSend(topic, msg, sendCallback, syncTimeout);
        } catch (Exception e) {
            log.error("向topic:{}发送异步消息失败", topic);
            throw new MQSendFailedException();
        }
        log.info("向topic:{}提交异步消息,message:{}",
                topic, msgStr);
    }

    /**
     * 发送同步消息
     *
     * <AUTHOR>
     * @date 2025/1/21 16:32
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessage(String topic, Object message) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .build();
        long s = System.currentTimeMillis();
        try {
            SendResult sendResult = rocketMQTemplate.syncSend(topic, msg, syncTimeout);
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("sendResult:{}", sendResult);
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("向topic:{}发送同步消息失败", topic);
            throw new MQSendFailedException();
        }
        long e = System.currentTimeMillis();
        log.info("向topic:{}发送同步消息成功,耗时:{}ms,,message:{}",
                topic, e - s, msgStr);
    }

    /**
     * 发送延时tag消息
     *
     * <AUTHOR>
     * @date 2025/1/21 16:32
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessage(String topic, String tag, Object message, DelayLevelEnums delayLevelEnums) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .setHeader("TAGS", tag)
                .build();
        long s = System.currentTimeMillis();
        try {
            SendResult sendResult = rocketMQTemplate.syncSend(topic, msg, syncTimeout, delayLevelEnums.getLevel());
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("sendResult:{}", sendResult);
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("向topic:{}发送延时消息失败,tag:{},延时时间:{}", topic, tag, delayLevelEnums.getDesc());
            throw new MQSendFailedException();
        }
        long e = System.currentTimeMillis();
        log.info("向topic:{}发送延时消息成功,耗时:{}ms,tag:{},message:{},延时时间:{}",
                topic, e - s, tag, msgStr, delayLevelEnums.getDesc());
    }

    /**
     * 发送异步延时tag消息
     *
     * <AUTHOR>
     * @date 2025/1/21 16:32
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessageAsync(String topic, String tag, Object message,
                                 DelayLevelEnums delayLevelEnums, SendCallback sendCallback) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .setHeader("TAGS", tag)
                .build();
        try {
            rocketMQTemplate.asyncSend(topic, msg, sendCallback, syncTimeout, delayLevelEnums.getLevel());
        } catch (Exception e) {
            log.error("向topic:{}提交延时消息失败,tag:{},延时时间:{}", topic, tag, delayLevelEnums.getDesc());
            throw new MQSendFailedException();
        }
        log.info("向topic:{}提交延时消息,tag:{},message:{},延时时间:{}",
                topic, tag, msgStr, delayLevelEnums.getDesc());
    }

    /**
     * 发送延时消息
     *
     * <AUTHOR>
     * @date 2025/1/21 16:32
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessage(String topic, Object message, DelayLevelEnums delayLevelEnums) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .build();
        long s = System.currentTimeMillis();
        try {
            SendResult sendResult = rocketMQTemplate.syncSend(topic, msg, syncTimeout, delayLevelEnums.getLevel());
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("sendResult:{}", sendResult);
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("向topic:{}发送延时消息失败,延时时间:{}", topic, delayLevelEnums.getDesc());
            throw new MQSendFailedException();
        }
        long e = System.currentTimeMillis();
        log.info("向topic:{}发送延时消息成功,耗时:{}ms,message:{},延时时间:{}",
                topic, e - s, msgStr, delayLevelEnums.getDesc());
    }

    /**
     * 发送异步延时消息
     *
     * <AUTHOR>
     * @date 2025/1/21 16:32
     * @company 昆仑数智科技有限责任公司
     */
    public void sendMessageAsync(String topic, Object message,
                                 DelayLevelEnums delayLevelEnums, SendCallback sendCallback) throws MQSendFailedException {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .build();
        try {
            rocketMQTemplate.asyncSend(topic, msg, sendCallback, syncTimeout, delayLevelEnums.getLevel());
        } catch (Exception e) {
            log.error("向topic:{}提交延时消息失败,延时时间:{}", topic, delayLevelEnums.getDesc());
            throw new MQSendFailedException();
        }
        log.info("向topic:{}提交延时消息,message:{},延时时间:{}",
                topic, msgStr, delayLevelEnums.getDesc());
    }

    /**
     * 发送事务消息,返回事务id
     * arg: 需要执行本地事务的业务对象
     *
     * <AUTHOR>
     * @date 2025/1/21 17:07
     * @company 昆仑数智科技有限责任公司
     */
    public String sendTransactionMessage(String topic, Object message, Object arg) {
        setContext(message);
        String msgStr = JSONUtils.toJSONString(message);
        String transactionId = UUID.randomUUID().toString();
        Message<String> msg = MessageBuilder
                .withPayload(msgStr)
                .setHeader(RocketMQHeaders.TRANSACTION_ID, transactionId)
                .build();
        long s = System.currentTimeMillis();
        rocketMQTemplate.sendMessageInTransaction(topic, msg, arg);
        long e = System.currentTimeMillis();
        log.info("向topic:{}发送事务消息成功,耗时:{}ms,message:{},arg:{}",
                topic, e - s, msgStr, JSONUtils.toJSONStringIgnoreError(arg));
        return transactionId;
    }

}
