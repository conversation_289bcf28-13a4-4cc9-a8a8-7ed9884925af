package com.cnpc.ymz.bff.cbase.rocketmq.enums;

import lombok.Getter;

@Getter
public enum DelayLevelEnums {

    SECONDS_1(1,"延时1秒"),
    SECONDS_5(2,"延时5秒"),
    SECONDS_10(3,"延时10秒"),
    SECONDS_30(4,"延时30秒"),
    MINUTES_1(5,"延时1分钟"),
    MINUTES_2(6,"延时2分钟"),
    MINUTES_3(7,"延时3分钟"),
    MINUTES_4(8,"延时4分钟"),
    MINUTES_5(9,"延时5分钟"),
    MINUTES_6(10,"延时6分钟"),
    MINUTES_7(11,"延时7分钟"),
    MINUTES_8(12,"延时8分钟"),
    MINUTES_9(13,"延时9分钟"),
    MINUTES_10(14,"延时10分钟"),
    MINUTES_20(15,"延时20分钟"),
    MINUTES_30(16,"延时30分钟"),
    HOURS_1(17,"延时1小时"),
    HOURS_2(18,"延时2小时"),
    ;

    private final int level;

    private String desc;

    DelayLevelEnums(int level,String desc) {
        this.level = level;
        this.desc = desc;
    }

}
