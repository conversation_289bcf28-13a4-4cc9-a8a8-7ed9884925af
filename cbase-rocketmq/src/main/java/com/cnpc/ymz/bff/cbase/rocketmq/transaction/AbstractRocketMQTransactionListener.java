package com.cnpc.ymz.bff.cbase.rocketmq.transaction;

import com.cnpc.ymz.bff.cbase.rocketmq.dto.MQBaseDTO;
import com.cnpc.ymz.bff.cbase.rocketmq.log.LogAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionListener;
import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.slf4j.MDC;
import org.springframework.messaging.Message;

/**
 * 本地事务执行抽象类
 * 子类需标注：@RocketMQTransactionListener
 * <AUTHOR>
 * @date 2025/2/27 09:38
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public abstract class AbstractRocketMQTransactionListener implements RocketMQLocalTransactionListener {

    protected abstract boolean doExecuteLocalTransaction(Object arg, String transactionId);

    protected abstract boolean doCheckLocalTransaction(String transactionId);

    @Override
    public RocketMQLocalTransactionState executeLocalTransaction(Message message, Object arg) {
        MQBaseDTO mqBaseDTO = (MQBaseDTO) message.getPayload();
        LogAdapter.setLog(mqBaseDTO);
        String transactionId = (String) message.getHeaders().get(RocketMQHeaders.TRANSACTION_ID);
        log.info("transactionId:{},即将执行本地事务", transactionId);
        try {
            boolean b = doExecuteLocalTransaction(arg, transactionId);
            if (b) {
                log.info("本地事务执行成功,COMMIT");
                return RocketMQLocalTransactionState.COMMIT;
            } else {
                log.info("本地事务执行失败,ROLLBACK");
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        } catch (Exception e) {
            log.error("本地事务执行发生异常,ROLLBACK", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        } finally {
            MDC.clear();
        }
    }

    @Override
    public RocketMQLocalTransactionState checkLocalTransaction(Message message) {
        MQBaseDTO mqBaseDTO = (MQBaseDTO) message.getPayload();
        LogAdapter.setLog(mqBaseDTO);
        String transactionId = (String) message.getHeaders().get(RocketMQHeaders.TRANSACTION_ID);
        log.info("transactionId:{},服务端进行本地事务状态查询", transactionId);
        try {
            boolean b = doCheckLocalTransaction(transactionId);
            if (b) {
                log.info("本地事务回查，执行成功,COMMIT");
                return RocketMQLocalTransactionState.COMMIT;
            } else {
                log.info("本地事务回查，执行失败，ROLLBACK");
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        } catch (Exception e) {
            log.error("本地事务回查异常,ROLLBACK", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        } finally {
            MDC.clear();
        }
    }
}
