package com.cnpc.ymz.bff.cbase.gateway.config;


import org.springframework.cloud.gateway.config.GatewayProperties;
import org.springframework.cloud.gateway.event.EnableBodyCachingEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 获取http-body必要配置
 * <AUTHOR>
 * @date 2025/1/17 09:24
 * @company 昆仑数智科技有限责任公司
 */
@Configuration(proxyBeanMethods = false)
public class GatewayCommonConfig {

    @Resource
    private ApplicationEventPublisher publisher;

    @Resource
    private GatewayProperties gatewayProperties;

    @PostConstruct
    public void init() {
        gatewayProperties.getRoutes().forEach(routeDefinition -> {
            EnableBodyCachingEvent enableBodyCachingEvent = new EnableBodyCachingEvent(new Object(), routeDefinition.getId());
            publisher.publishEvent(enableBodyCachingEvent);
        });
    }
}
