package com.cnpc.ymz.bff.cbase.gateway.util;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.util.retry.Retry;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 可配置的响应式HTTP工具类
 * 支持通过配置文件自定义连接池和超时参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class ConfigurableReactiveHttpUtils {

    @Autowired
    private ReactiveHttpConfig config;

    private WebClient webClient;

    private ConnectionProvider connectionProvider;

    /**
     * 初始化WebClient
     */
    @PostConstruct
    public void init() {
        this.connectionProvider = createConnectionProvider();
        this.webClient = createWebClient();
        log.info("ConfigurableReactiveHttpUtils初始化完成，连接池配置: maxConnections={}, connectTimeout={}ms, readTimeout={}s", 
                config.getPool().getMaxConnections(), 
                config.getTimeout().getConnect(), 
                config.getTimeout().getRead());
    }

    /**
     * 创建连接池提供者
     */
    private ConnectionProvider createConnectionProvider() {
        return ConnectionProvider.builder("configurable-http-pool")
                .maxConnections(config.getPool().getMaxConnections())
                .pendingAcquireTimeout(config.getPendingAcquireTimeoutDuration())
                .maxIdleTime(config.getMaxIdleTimeDuration())
                .maxLifeTime(config.getMaxLifeTimeDuration())
                .build();
    }

    /**
     * 创建WebClient实例
     */
    private WebClient createWebClient() {
        HttpClient httpClient = HttpClient.create(connectionProvider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getTimeout().getConnect())
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(config.getTimeout().getRead(), TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(config.getTimeout().getWrite(), TimeUnit.SECONDS)));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    /**
     * 执行GET请求
     */
    public <T> Mono<T> get(String url, Class<T> responseType) {
        return get(url, null, responseType);
    }

    /**
     * 执行GET请求（带请求头）
     */
    public <T> Mono<T> get(String url, Map<String, String> headers, Class<T> responseType) {
        return executeRequest(HttpMethod.GET, url, headers, null, responseType);
    }

    /**
     * 执行POST请求
     */
    public <T> Mono<T> post(String url, Object requestBody, Class<T> responseType) {
        return post(url, null, requestBody, responseType);
    }

    /**
     * 执行POST请求（带请求头）
     */
    public <T> Mono<T> post(String url, Map<String, String> headers, Object requestBody, Class<T> responseType) {
        return executeRequest(HttpMethod.POST, url, headers, requestBody, responseType);
    }

    /**
     * 执行PUT请求
     */
    public <T> Mono<T> put(String url, Object requestBody, Class<T> responseType) {
        return put(url, null, requestBody, responseType);
    }

    /**
     * 执行PUT请求（带请求头）
     */
    public <T> Mono<T> put(String url, Map<String, String> headers, Object requestBody, Class<T> responseType) {
        return executeRequest(HttpMethod.PUT, url, headers, requestBody, responseType);
    }

    /**
     * 执行DELETE请求
     */
    public <T> Mono<T> delete(String url, Class<T> responseType) {
        return delete(url, null, responseType);
    }

    /**
     * 执行DELETE请求（带请求头）
     */
    public <T> Mono<T> delete(String url, Map<String, String> headers, Class<T> responseType) {
        return executeRequest(HttpMethod.DELETE, url, headers, null, responseType);
    }

    /**
     * 执行表单POST请求
     */
    public <T> Mono<T> postForm(String url, MultiValueMap<String, String> formData, Class<T> responseType) {
        return postForm(url, null, formData, responseType);
    }

    /**
     * 执行表单POST请求（带请求头）
     */
    public <T> Mono<T> postForm(String url, Map<String, String> headers, MultiValueMap<String, String> formData, Class<T> responseType) {
        WebClient.RequestBodySpec requestSpec = webClient.method(HttpMethod.POST)
                .uri(url)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestSpec::header);
        }

        return requestSpec
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .bodyToMono(responseType)
                .retryWhen(Retry.backoff(config.getRetry().getAttempts(), config.getRetryBackoffDuration()))
                .doOnError(error -> log.error("HTTP表单请求失败: {}, URL: {}", error.getMessage(), url))
                .onErrorMap(WebClientResponseException.class, this::handleWebClientException);
    }

    /**
     * 通用HTTP请求执行方法
     */
    private <T> Mono<T> executeRequest(HttpMethod method, String url, Map<String, String> headers, 
                                      Object requestBody, Class<T> responseType) {
        WebClient.RequestBodySpec requestSpec = webClient.method(method)
                .uri(url);

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestSpec::header);
        }

        // 设置默认Content-Type
        if (requestBody != null && (headers == null || !headers.containsKey(HttpHeaders.CONTENT_TYPE))) {
            requestSpec.contentType(MediaType.APPLICATION_JSON);
        }

        // 添加请求体
        WebClient.RequestHeadersSpec<?> headersSpec;
        if (requestBody != null) {
            headersSpec = requestSpec.bodyValue(requestBody);
        } else {
            headersSpec = requestSpec;
        }

        return headersSpec
                .retrieve()
                .bodyToMono(responseType)
                .retryWhen(Retry.backoff(config.getRetry().getAttempts(), config.getRetryBackoffDuration()))
                .doOnError(error -> log.error("HTTP请求失败: {}, URL: {}, Method: {}", 
                          error.getMessage(), url, method))
                .onErrorMap(WebClientResponseException.class, this::handleWebClientException);
    }

    /**
     * 处理WebClient异常
     */
    private RuntimeException handleWebClientException(WebClientResponseException ex) {
        String errorMessage = String.format("HTTP请求失败: 状态码=%d, 响应体=%s", 
                                           ex.getStatusCode().value(), ex.getResponseBodyAsString());
        log.error(errorMessage, ex);
        return new RuntimeException(errorMessage, ex);
    }

    /**
     * 获取字符串响应
     */
    public Mono<String> getString(String url) {
        return get(url, String.class);
    }

    /**
     * 获取字符串响应（带请求头）
     */
    public Mono<String> getString(String url, Map<String, String> headers) {
        return get(url, headers, String.class);
    }

    /**
     * POST JSON并获取字符串响应
     */
    public Mono<String> postForString(String url, Object requestBody) {
        return post(url, requestBody, String.class);
    }

    /**
     * POST JSON并获取字符串响应（带请求头）
     */
    public Mono<String> postForString(String url, Map<String, String> headers, Object requestBody) {
        return post(url, headers, requestBody, String.class);
    }

    /**
     * 销毁资源
     */
    @PreDestroy
    public void destroy() {
        if (connectionProvider != null) {
            connectionProvider.dispose();
            log.info("ConfigurableReactiveHttpUtils连接池已关闭");
        }
    }
}
