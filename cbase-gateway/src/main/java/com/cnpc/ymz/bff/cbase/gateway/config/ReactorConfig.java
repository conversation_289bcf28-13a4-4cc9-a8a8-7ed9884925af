package com.cnpc.ymz.bff.cbase.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorResourceFactory;

/**
 * 网关性能优化配置
 * <AUTHOR>
 * @date 2025/1/17 09:24
 * @company 昆仑数智科技有限责任公司
 */
@Configuration
@Slf4j
public class ReactorConfig {

    @Bean
    public ReactorResourceFactory reactorClientResourceFactory() {
        System.setProperty("reactor.netty.ioSelectCount", "1");
        int ioWorkerCount = (Runtime.getRuntime().availableProcessors()) * 3;
        System.setProperty("reactor.netty.ioWorkerCount", String.valueOf(ioWorkerCount));
        log.info("reactor.netty.ioWorkerCount:{}", ioWorkerCount);
        return new ReactorResourceFactory();
    }
}
