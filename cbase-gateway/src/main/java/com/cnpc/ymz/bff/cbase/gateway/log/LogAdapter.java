package com.cnpc.ymz.bff.cbase.gateway.log;

import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;


/**
 * 日志适配器
 *
 * <AUTHOR>
 * @date 2025/5/27 10:07
 * @company 昆仑数智科技有限责任公司
 */
public class LogAdapter {

    public static void setLog(ContextInfo contextInfo) {
        String logInfo = contextInfo.getUserId() + "|" +
                contextInfo.getTraceId() + "|" +
                contextInfo.getClientCode();
        MDC.remove("LOG");
        MDC.put("LOG", logInfo);
    }

    public static void setLog(String userId, String traceId, String clientCode) {
        String logInfo = userId + "|" +
                traceId + "|" +
                clientCode;
        MDC.remove("LOG");
        MDC.put("LOG", logInfo);
    }

    public static boolean isNull() {
        return StringUtils.isBlank(MDC.get("LOG"));
    }
}
