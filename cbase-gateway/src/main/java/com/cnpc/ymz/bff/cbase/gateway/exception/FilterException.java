package com.cnpc.ymz.bff.cbase.gateway.exception;


public class FilterException extends Exception {

    private final String code;

    public FilterException() {
        super();
        code = "000000";
    }

    public FilterException(String message) {
        super(message);
        code = "000000";
    }

    public FilterException(String code, String message) {
        super(message);
        this.code = code;
    }

    public FilterException(String message, Throwable cause) {
        super(message, cause);
        code = "000000";
    }

    public FilterException(Throwable cause) {
        super(cause);
        code = "000000";
    }

    public String getCode() {
        return code;
    }
}
