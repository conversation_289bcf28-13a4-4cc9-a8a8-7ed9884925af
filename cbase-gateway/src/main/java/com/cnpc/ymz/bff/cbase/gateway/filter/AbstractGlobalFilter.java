package com.cnpc.ymz.bff.cbase.gateway.filter;


import com.cnpc.ymz.bff.cbase.common.dto.ResponseDTO;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.gateway.exception.FilterException;
import com.cnpc.ymz.bff.cbase.gateway.log.LogAdapter;
import com.cnpc.ymz.bff.cbase.gateway.util.GatewayUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Publisher;
import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.cloud.gateway.support.TimeoutException;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ReactiveHttpOutputMessage;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 业务全局过滤器
 *
 * <AUTHOR>
 * @date 2025/5/27 10:07
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
public abstract class AbstractGlobalFilter implements GlobalFilter, GatewayFilter, Ordered {

    private final List<HttpMessageReader<?>> messageReaders = HandlerStrategies.withDefaults().messageReaders();

    /**
     * 业务过滤逻辑
     *
     * <AUTHOR>
     * @date 2025/1/17 14:05
     * @company 昆仑数智科技有限责任公司
     */
    protected abstract void filter(ServerWebExchange exchange, ChainDTO chainDTO) throws FilterException;

    /**
     * 转化response-body
     *
     * <AUTHOR>
     * @date 2025/1/17 14:05
     * @company 昆仑数智科技有限责任公司
     */
    protected abstract String changeResponseBody(String origBody);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        MDC.clear();
        // route路由只作http纯转发,直接放行
        String path = exchange.getRequest().getURI().getPath();
        if (path.startsWith("/route/")) {
            log.info("转发请求:{}", path);
            return chain.filter(exchange);
        }

        MediaType contentType = exchange.getRequest().getHeaders().getContentType();
        long startTime = System.currentTimeMillis();
        ChainDTO chainDTO = new ChainDTO();
        try {
            filter(exchange, chainDTO);
            LogAdapter.setLog(chainDTO.getContextInfo());
        } catch (FilterException filterException) {
            if (LogAdapter.isNull()) {
                LogAdapter.setLog(chainDTO.getContextInfo());
            }
            String code = filterException.getCode();
            if (StringUtils.isBlank(code)) {
                code = "P_" + getAppCode(exchange) + "_000000";
            } else if (!code.contains("_")) {
                code = "P_" + getAppCode(exchange) + "_" + code;
            }
            log.error(filterException.getMessage(), filterException);
            return fail(exchange, code, filterException.getMessage());
        } catch (Exception e) {
            if (LogAdapter.isNull()) {
                LogAdapter.setLog(chainDTO.getContextInfo());
            }
            String appCode = getAppCode(exchange);
            log.error("gatewayError:", e);
            return fail(exchange, "S_" + appCode + "_00000", "系统异常");
        }

        // 构造新的header
        HttpHeaders newHeader = GatewayUtils.buildServiceHeader(chainDTO.getContextInfo());
        if (isJsonRequest(contentType)) {
            // 处理application-json
            String fBody = StringUtils.isBlank(chainDTO.getHttpBody()) ? GatewayUtils.getHttpBody(exchange) : chainDTO.getHttpBody();
            ServerRequest serverRequest = ServerRequest.create(exchange, messageReaders);
            Mono<byte[]> modifiedBody = serverRequest.bodyToMono(byte[].class).flatMap(s -> {
                byte[] bytes = fBody.getBytes(StandardCharsets.UTF_8);
                return Mono.just(bytes);
            });
            newHeader.setContentLength(fBody.getBytes().length);
            BodyInserter<Mono<byte[]>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromPublisher(modifiedBody, byte[].class);
            newHeader.setContentType(contentType);
            CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, newHeader);
            return bodyInserter.insert(outputMessage, new BodyInserterContext())
                    .then(Mono.defer(() -> {
                        // 重新封装请求
                        ServerHttpRequest decoratedRequest = requestDecorate(exchange, newHeader, outputMessage);
                        // 记录响应日志
                        ServerHttpResponseDecorator decoratedResponse = recordResponseLog(exchange, chainDTO, startTime);
                        printRequestLog(newHeader, fBody, path);
                        return getMono(exchange, chain, chainDTO, decoratedRequest, decoratedResponse);
                    }));
        } else {
            // 处理其余content-type
            if (exchange.getRequest().getHeaders().containsKey("Content-Length")) {
                // 如果设置了Content-Length，则透传
                newHeader.setContentLength(exchange.getRequest().getHeaders().getContentLength());
            } else if (exchange.getRequest().getHeaders().containsKey("Transfer-Encoding")) {
                // 分块传输时移除Content-Length
                newHeader.remove("Content-Length");
            }
            // 文件流直接传递二进制数据，不修改内容
            BodyInserter<Flux<DataBuffer>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromDataBuffers(exchange.getRequest().getBody());
            newHeader.setContentType(contentType);
            CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, newHeader);
            return bodyInserter.insert(outputMessage, new BodyInserterContext())
                    .then(Mono.defer(() -> {
                        // 重新封装请求
                        ServerHttpRequest decoratedRequest = requestDecorate(exchange, newHeader, outputMessage);
                        // 记录响应日志
                        ServerHttpResponseDecorator decoratedResponse = recordResponseLog(exchange, chainDTO, startTime);
                        printRequestLog(newHeader, "", path);
                        return getMono(exchange, chain, chainDTO, decoratedRequest, decoratedResponse);
                    }));
        }

    }

    private Mono<? extends Void> getMono(ServerWebExchange exchange, GatewayFilterChain chain, ChainDTO chainDTO, ServerHttpRequest decoratedRequest, ServerHttpResponseDecorator decoratedResponse) {
        return chain.filter(exchange.mutate().request(decoratedRequest).response(decoratedResponse).build())
                .onErrorResume(e -> {
                    LogAdapter.setLog(chainDTO.getContextInfo());
                    if (e.getCause() != null && e.getCause() instanceof TimeoutException) {
                        // 捕获下游服务超时异常
                        log.error("下游服务处理超时", e);
                        String appCode = getAppCode(exchange);
                        return fail(exchange, "S_" + appCode + "_TIMEOUT", "系统处理超时,请稍后重试");
                    } else {
                        // 其他异常处理
                        log.error("网关请求下游服务异常", e);
                        String appCode = getAppCode(exchange);
                        return fail(exchange, "S_" + appCode + "_ERROR", "系统异常,请稍后重试");
                    }
                }).doFinally(e -> MDC.clear());
    }

    private Mono<Void> fail(ServerWebExchange exchange, String code, String message, String... data) {
        log.error("{}:{}", code, message);
        ResponseDTO<String> responseDTO = new ResponseDTO<>();
        responseDTO.setSuccess(false);
        responseDTO.setErrorCode(code);
        responseDTO.setMessage(message);
        if (data != null && data.length > 0) {
            responseDTO.setData(data[0]);
        } else {
            responseDTO.setData("");
        }
        byte[] bits = JSONUtils.toJSONString(responseDTO).getBytes(StandardCharsets.UTF_8);
        ServerHttpResponse serverHttpresponse = exchange.getResponse();
        DataBuffer buffer = serverHttpresponse.bufferFactory().wrap(bits);
        serverHttpresponse.setStatusCode(HttpStatus.OK);
        serverHttpresponse.getHeaders().add("Content-Type", "application/json");
        return serverHttpresponse.writeWith(Mono.just(buffer)).doFinally(e -> MDC.clear());
    }

    private ServerHttpRequestDecorator requestDecorate(ServerWebExchange exchange, HttpHeaders headers,
                                                       CachedBodyOutputMessage outputMessage) {
        return new ServerHttpRequestDecorator(exchange.getRequest()) {
            @Override
            public HttpHeaders getHeaders() {
                return headers;
            }

            @Override
            public Flux<DataBuffer> getBody() {
                return outputMessage.getBody();
            }
        };
    }

    private void printRequestLog(HttpHeaders httpHeaders, String body, String uri) {
        Map<String, String> headers = new HashMap<>();
        httpHeaders.forEach((name, value) -> {
            headers.put(name, value.get(0));
        });
        if (StringUtils.isNotBlank(body)) {
            log.info("请求url:{},网关出口请求header:{},网关出口请求body:{}", uri, JSONUtils.toJSONStringIgnoreError(headers), body);
        } else {
            log.info("请求url:{},网关出口请求header:{}", uri, JSONUtils.toJSONStringIgnoreError(headers));
        }
    }

    private ServerHttpResponseDecorator recordResponseLog(ServerWebExchange exchange, ChainDTO dto, long start) {
        ServerHttpResponse response = exchange.getResponse();
        DataBufferFactory bufferFactory = response.bufferFactory();
        return new ServerHttpResponseDecorator(response) {
            @Override
            @NonNull
            public Mono<Void> writeWith(@NonNull Publisher<? extends DataBuffer> body) {
                if (body instanceof Flux) {
                    if ((Objects.equals(this.getStatusCode(), HttpStatus.OK)
                            || Objects.equals(this.getStatusCode(), HttpStatus.INTERNAL_SERVER_ERROR)) &&
                            response.getHeaders().getContentType() != null &&
                            response.getHeaders().getContentType().toString().contains(MediaType.APPLICATION_JSON_VALUE)) {
                        Flux<? extends DataBuffer> fluxBody = Flux.from(body);
                        return super.writeWith(fluxBody.buffer().map(dataBuffers -> {

                            LogAdapter.setLog(dto.getContextInfo());

                            // 获取response-body
                            String result = "";
                            DataBufferFactory dataBufferFactory = getDelegate().bufferFactory();
                            DataBuffer join = dataBufferFactory.join(dataBuffers);
                            byte[] content = new byte[join.readableByteCount()];
                            join.read(content);
                            DataBufferUtils.release(join);
                            result = new String(content, StandardCharsets.UTF_8);


                            // body转换
                            result = changeResponseBody(result);

                            log.info("响应耗时[{}]ms,url:{},响应body:{}", System.currentTimeMillis() - start,
                                    exchange.getRequest().getURI().getPath(), result);

                            // 重新设置content-length
                            byte[] newRs = result.getBytes(StandardCharsets.UTF_8);
                            this.getDelegate().getHeaders().setContentLength(newRs.length);
                            return bufferFactory.wrap(newRs);
                        }).doOnDiscard(DataBuffer.class, DataBufferUtils::release)).doFinally(e -> MDC.clear());
                    } else {
                        LogAdapter.setLog(dto.getContextInfo());
                        log.info("响应耗时[{}]ms,url:{}", System.currentTimeMillis() - start, exchange.getRequest().getURI().getPath());
                        return super.writeWith(body).doFinally(e -> MDC.clear());
                    }
                }
                return super.writeWith(body);
            }
        };
    }

    @Override
    public int getOrder() {
        return -100;
    }

    private String getAppCode(ServerWebExchange exchange) {
        String appCode = "GW";
        ApplicationContext applicationContext = exchange.getApplicationContext();
        if (applicationContext != null) {
            String str = applicationContext.getEnvironment().getProperty("app.code");
            if (StringUtils.isNotBlank(str)) {
                appCode = str;
            }
        }
        return appCode;
    }

    private boolean isJsonRequest(MediaType contentType) {
        return contentType != null && contentType.includes(MediaType.APPLICATION_JSON);
    }

}
