package com.cnpc.ymz.bff.cbase.gateway.context;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kld.foundation.utils.AppContext;
import com.kld.foundation.utils.context.KldInvocationContext;
import lombok.Data;

@Data
public class ContextHeaderDTO {

    @JsonProperty(value = "Authorization")
    private String authorization;

    @JsonProperty(value = KldInvocationContext.CONTEXT_ORIG_IP)
    private String ip;

    @JsonProperty(value = "client-code")
    private String clientCode;

    @JsonProperty(value = KldInvocationContext.CONTEXT_TRACE_ID)
    private String traceId;

    @JsonProperty(value = KldInvocationContext.CONTEXT_INSTANCE_ID)
    private String instanceId;

    @JsonProperty(value = AppContext.USER_INFO)
    private String userInfo;

    @JsonProperty(value = "x-token")
    private String xToken;

    @JsonProperty(value = "x-gray")
    private String xGray;

    @JsonProperty(value = KldInvocationContext.CONTEXT_MICROSERVICE_NAME)
    private String serviceName;

    @JsonProperty(value = AppContext.API_VERSION)
    private String apiVersion;

    @JsonProperty(value = "risk-field")
    private String riskField;

}
