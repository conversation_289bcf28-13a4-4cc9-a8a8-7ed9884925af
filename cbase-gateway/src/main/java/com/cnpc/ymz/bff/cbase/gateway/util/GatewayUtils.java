package com.cnpc.ymz.bff.cbase.gateway.util;

import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.HeaderUtils;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.gateway.context.ContextHeaderDTO;
import com.kld.foundation.utils.context.KldInvocationContextHolder;
import com.kld.foundation.utils.security.UserType;
import com.kld.foundation.utils.user.PetroMemberInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Objects;

@Slf4j
public class GatewayUtils {

    /**
     * 获取http-body
     *
     * <AUTHOR>
     * @date 2025/1/17 09:28
     * @company 昆仑数智科技有限责任公司
     */
    public static String getHttpBody(ServerWebExchange serverWebExchange) {
        DataBuffer body = serverWebExchange.getAttribute("cachedRequestBody");
        if (body == null) {
            log.warn("http请求体为空");
            return "";
        }
        return body.toString(StandardCharsets.UTF_8);
    }

    /**
     * 获取客户端原始ip
     *
     * <AUTHOR>
     * @date 2025/1/17 09:28
     * @company 昆仑数智科技有限责任公司
     */
    public static String getOrigIp(ServerWebExchange serverWebExchange) {
        ServerHttpRequest request = serverWebExchange.getRequest();
        HttpHeaders headers = request.getHeaders();
        String ip = headers.getFirst("x-forwarded-for");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            if (ip.contains(",")) {
                ip = ip.split(",")[0];
            }
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("X-Real-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = Objects.requireNonNull(request.getRemoteAddress()).getAddress().getHostAddress();
        }
        return ip;
    }

    /**
     * 构建gateway向下游服务请求的header
     *
     * <AUTHOR>
     * @date 2025/1/17 10:11
     * @company 昆仑数智科技有限责任公司
     */
    public static HttpHeaders buildServiceHeader(ContextInfo contextInfo) {
        HttpHeaders result = new HttpHeaders();
        result.put("user-info", Collections.singletonList(contextInfo.getUserId()));
        result.put("orig-ip", Collections.singletonList(contextInfo.getIp()));
        result.put("device-name", Collections.singletonList(contextInfo.getDeviceName()));
        result.put("device-no", Collections.singletonList(contextInfo.getDeviceNo()));
        result.put("device-id", Collections.singletonList(contextInfo.getDeviceId()));
        result.put("api-version", Collections.singletonList(contextInfo.getApiVersion()));
        result.put("source-code", Collections.singletonList(contextInfo.getSourceCode()));
        result.put("client-code", Collections.singletonList(contextInfo.getClientCode()));
        result.put("time", Collections.singletonList(contextInfo.getTime()));
        result.put("trace-id", Collections.singletonList(contextInfo.getTraceId()));
        result.put("authorization", Collections.singletonList(contextInfo.getAuthorization()));
        String userName = contextInfo.getUserName();
        String riskField = contextInfo.getRiskField();
        if (StringUtils.isNotBlank(userName)) {
            result.put("user-name", Collections.singletonList(HeaderUtils.encode(userName)));
        }
        if (StringUtils.isNotBlank(riskField)) {
            result.put("risk-field", Collections.singletonList(HeaderUtils.encode(riskField)));
        }
        if (contextInfo.getOthers() != null && !contextInfo.getOthers().isEmpty()) {
            String others = HeaderUtils.encode(JSONUtils.toJSONString(contextInfo.getOthers()));
            result.put("other-context", Collections.singletonList(others));
        }
        // 为了兼容其他服务，增加新的上下文字段
        String json = JSONUtils.toJSONString(getFeignHeaderDTO(contextInfo));
        String str = URLEncoder.encode(json);
        result.put(KldInvocationContextHolder.ATTRIBUTE_KEY, Collections.singletonList(str));
        return result;
    }

    private static ContextHeaderDTO getFeignHeaderDTO(ContextInfo contextInfo) {
        PetroMemberInfo petroMemberInfo = new PetroMemberInfo();
        boolean login = false;
        if (StringUtils.isNotBlank(contextInfo.getUserId())) {
            petroMemberInfo.setUserId(Long.parseLong(contextInfo.getUserId()));
            petroMemberInfo.setUserType(UserType.OUTER);
            petroMemberInfo.setUsername(contextInfo.getUserName());
            login = true;
        }
        ContextHeaderDTO feignHeaderDTO = new ContextHeaderDTO();
        feignHeaderDTO.setIp(contextInfo.getIp());
        feignHeaderDTO.setClientCode(contextInfo.getClientCode());
        feignHeaderDTO.setTraceId(contextInfo.getTraceId());
        feignHeaderDTO.setApiVersion(contextInfo.getApiVersion());
        feignHeaderDTO.setRiskField(contextInfo.getRiskField());
        if (login) {
            feignHeaderDTO.setAuthorization(contextInfo.getAuthorization());
            feignHeaderDTO.setXToken(contextInfo.getAuthorization());
            feignHeaderDTO.setUserInfo(JSONUtils.toJSONString(petroMemberInfo));
        }
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            feignHeaderDTO.setInstanceId(inetAddress.getHostAddress().replaceAll("\\.", "-"));
        } catch (Exception e) {
            log.warn("获取实例ip失败");
        }
        return feignHeaderDTO;
    }
}
