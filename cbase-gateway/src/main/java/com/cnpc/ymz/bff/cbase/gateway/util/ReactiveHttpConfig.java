package com.cnpc.ymz.bff.cbase.gateway.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * ReactiveHttp配置类
 * 支持通过配置文件自定义HTTP客户端参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "reactive.http")
public class ReactiveHttpConfig {

    /**
     * 连接池配置
     */
    private Pool pool = new Pool();

    /**
     * 超时配置
     */
    private Timeout timeout = new Timeout();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    @Data
    public static class Pool {
        /**
         * 最大连接数
         */
        private int maxConnections = 100;

        /**
         * 获取连接超时时间(秒)
         */
        private int pendingAcquireTimeout = 45;

        /**
         * 连接最大空闲时间(秒)
         */
        private int maxIdleTime = 20;

        /**
         * 连接最大生存时间(分钟)
         */
        private int maxLifeTime = 5;
    }

    @Data
    public static class Timeout {
        /**
         * 连接超时时间(毫秒)
         */
        private int connect = 5000;

        /**
         * 读取超时时间(秒)
         */
        private int read = 30;

        /**
         * 写入超时时间(秒)
         */
        private int write = 30;
    }

    @Data
    public static class Retry {
        /**
         * 重试次数
         */
        private int attempts = 3;

        /**
         * 重试间隔(秒)
         */
        private int backoffSeconds = 1;
    }

    /**
     * 获取连接池最大空闲时间Duration对象
     */
    public Duration getMaxIdleTimeDuration() {
        return Duration.ofSeconds(pool.maxIdleTime);
    }

    /**
     * 获取连接池最大生存时间Duration对象
     */
    public Duration getMaxLifeTimeDuration() {
        return Duration.ofMinutes(pool.maxLifeTime);
    }

    /**
     * 获取获取连接超时时间Duration对象
     */
    public Duration getPendingAcquireTimeoutDuration() {
        return Duration.ofSeconds(pool.pendingAcquireTimeout);
    }

    /**
     * 获取重试间隔Duration对象
     */
    public Duration getRetryBackoffDuration() {
        return Duration.ofSeconds(retry.backoffSeconds);
    }
}
