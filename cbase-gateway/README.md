# ReactiveHttp工具类使用指南

## 概述

本项目提供了两个基于Spring WebFlux的响应式HTTP工具类，用于进行非阻塞IO的HTTP访问，并支持连接池管理以减少HTTP连接的建立开销。

## 工具类介绍

### 1. ReactiveHttpUtils
- **特点**: 单例模式，使用默认配置
- **适用场景**: 简单的HTTP调用，不需要复杂配置
- **连接池**: 自动管理，使用默认参数

### 2. ConfigurableReactiveHttpUtils
- **特点**: Spring Bean，支持配置文件配置
- **适用场景**: 需要自定义连接池和超时参数的场景
- **连接池**: 可通过配置文件自定义参数

## 主要功能

### 支持的HTTP方法
- GET请求
- POST请求（JSON和表单）
- PUT请求
- DELETE请求

### 连接池特性
- 连接复用，减少连接建立开销
- 可配置最大连接数
- 连接超时管理
- 连接生命周期管理
- 自动重试机制

### 非阻塞特性
- 基于Reactor的响应式编程
- 支持链式调用
- 支持并发请求
- 异步错误处理

## 使用方式

### 1. 使用ConfigurableReactiveHttpUtils（Spring Bean）

```java
@Service
public class UserService {
    
    @Autowired
    private ConfigurableReactiveHttpUtils httpUtils;
    
    public Mono<User> getUserById(String userId) {
        return httpUtils.get("https://api.example.com/users/" + userId, User.class);
    }
    
    public Mono<User> createUser(User user) {
        return httpUtils.post("https://api.example.com/users", user, User.class);
    }
}
```

### 3. 配置文件配置

在`application.yml`中添加配置：

```yaml
reactive:
  http:
    pool:
      max-connections: 100
      pending-acquire-timeout: 45
      max-idle-time: 20
      max-life-time: 5
    timeout:
      connect: 5000
      read: 30
      write: 30
    retry:
      attempts: 3
      backoff-seconds: 1
```

### 4. 响应式编程示例

```java
// 链式调用
httpUtils.getString("https://api.example.com/uuid")
    .flatMap(uuid -> httpUtils.post("https://api.example.com/process", 
                                   Map.of("uuid", uuid), String.class))
    .subscribe(
        result -> log.info("处理结果: {}", result),
        error -> log.error("处理失败", error)
    );

// 并发请求
Mono<String> request1 = httpUtils.getString("https://api.example.com/data1");
Mono<String> request2 = httpUtils.getString("https://api.example.com/data2");

Mono.zip(request1, request2)
    .subscribe(tuple -> {
        String data1 = tuple.getT1();
        String data2 = tuple.getT2();
        // 处理合并结果
    });

// 错误处理
httpUtils.getString("https://api.example.com/data")
    .onErrorResume(error -> {
        log.warn("请求失败，使用默认值: {}", error.getMessage());
        return Mono.just("默认值");
    })
    .subscribe(result -> log.info("最终结果: {}", result));
```

## 配置参数说明

### 连接池配置
- `max-connections`: 最大连接数，默认100
- `pending-acquire-timeout`: 获取连接超时时间(秒)，默认45
- `max-idle-time`: 连接最大空闲时间(秒)，默认20
- `max-life-time`: 连接最大生存时间(分钟)，默认5

### 超时配置
- `connect`: 连接超时时间(毫秒)，默认5000
- `read`: 读取超时时间(秒)，默认30
- `write`: 写入超时时间(秒)，默认30

### 重试配置
- `attempts`: 重试次数，默认3
- `backoff-seconds`: 重试间隔(秒)，默认1

## 最佳实践

1. **选择合适的工具类**
   - 简单场景使用`ReactiveHttpUtils`
   - 需要自定义配置使用`ConfigurableReactiveHttpUtils`

2. **合理配置连接池**
   - 根据并发量调整`max-connections`
   - 适当设置连接超时时间
   - 监控连接池使用情况

3. **错误处理**
   - 使用`onErrorResume`提供降级方案
   - 合理设置重试次数和间隔
   - 记录详细的错误日志

4. **性能优化**
   - 复用HTTP工具实例
   - 避免阻塞操作
   - 使用并发请求提高效率

## 注意事项

1. 工具类基于Spring WebFlux，需要确保项目中包含相关依赖
2. 连接池会在应用程序关闭时自动清理
3. 建议在生产环境中根据实际负载调整配置参数
4. 使用响应式编程时注意避免阻塞操作

## 依赖要求

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>
<dependency>
    <groupId>io.projectreactor.netty</groupId>
    <artifactId>reactor-netty-http</artifactId>
</dependency>
```
