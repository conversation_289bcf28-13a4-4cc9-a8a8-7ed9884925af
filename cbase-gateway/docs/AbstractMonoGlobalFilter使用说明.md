# AbstractMonoGlobalFilter 使用说明

## 概述

`AbstractMonoGlobalFilter` 是 `AbstractGlobalFilter` 的非阻塞版本，专门为需要在网关过滤器中进行外部系统调用的场景设计。它将原有的同步 `filter` 方法改为返回 `Mono<Void>` 的异步方法，确保整个调用链都是非阻塞的。

## 主要变化

### 1. 抽象方法签名变化

**原版本 (AbstractGlobalFilter):**
```java
protected abstract void filter(ServerWebExchange exchange, ChainDTO chainDTO) throws FilterException;
```

**新版本 (AbstractMonoGlobalFilter):**
```java
protected abstract Mono<Void> filter(ServerWebExchange exchange, ChainDTO chainDTO);
```

### 2. 异常处理方式变化

- **原版本**: 使用 try-catch 块处理 FilterException
- **新版本**: 使用 Reactor 的 `onErrorResume` 操作符处理异常

### 3. 执行流程变化

- **原版本**: 同步执行业务逻辑，然后继续处理请求
- **新版本**: 异步执行业务逻辑，使用 `then()` 操作符连接后续处理

## 使用方式

### 1. 继承 AbstractMonoGlobalFilter

```java
@Slf4j
@Component
public class YourMonoGlobalFilter extends AbstractMonoGlobalFilter {
    
    @Override
    protected Mono<Void> filter(ServerWebExchange exchange, ChainDTO chainDTO) {
        // 实现您的业务逻辑
        return yourAsyncBusinessLogic(exchange, chainDTO);
    }
    
    @Override
    protected String changeResponseBody(String origBody) {
        // 实现响应体转换逻辑
        return origBody;
    }
}
```

### 2. 实现非阻塞外部调用

```java
@Override
protected Mono<Void> filter(ServerWebExchange exchange, ChainDTO chainDTO) {
    return webClient.get()
            .uri("/external-api")
            .retrieve()
            .bodyToMono(String.class)
            .doOnSuccess(result -> {
                // 处理成功响应
                chainDTO.getContextInfo().setClientCode(result);
            })
            .doOnError(error -> {
                // 处理错误
                log.error("外部调用失败", error);
            })
            .then(); // 转换为 Mono<Void>
}
```

### 3. 异常处理

如果需要抛出 FilterException，可以使用 `Mono.error()`:

```java
@Override
protected Mono<Void> filter(ServerWebExchange exchange, ChainDTO chainDTO) {
    String authHeader = exchange.getRequest().getHeaders().getFirst("Authorization");
    
    if (StringUtils.isBlank(authHeader)) {
        return Mono.error(new FilterException("AUTH001", "缺少认证头"));
    }
    
    return validateToken(authHeader)
            .flatMap(isValid -> {
                if (!isValid) {
                    return Mono.error(new FilterException("AUTH002", "认证失败"));
                }
                return Mono.empty();
            });
}
```

## 完整示例

```java
@Slf4j
@Component
public class AuthenticationMonoFilter extends AbstractMonoGlobalFilter {
    
    private final WebClient authServiceClient;
    
    public AuthenticationMonoFilter() {
        this.authServiceClient = WebClient.builder()
                .baseUrl("http://auth-service")
                .build();
    }
    
    @Override
    protected Mono<Void> filter(ServerWebExchange exchange, ChainDTO chainDTO) {
        String path = exchange.getRequest().getURI().getPath();
        
        // 跳过公开接口
        if (path.startsWith("/public/")) {
            return Mono.empty();
        }
        
        String token = exchange.getRequest().getHeaders().getFirst("Authorization");
        if (StringUtils.isBlank(token)) {
            return Mono.error(new FilterException("AUTH001", "缺少认证令牌"));
        }
        
        // 异步验证令牌
        return authServiceClient.post()
                .uri("/validate")
                .bodyValue(Map.of("token", token))
                .retrieve()
                .bodyToMono(AuthResponse.class)
                .timeout(Duration.ofSeconds(3))
                .flatMap(authResponse -> {
                    if (!authResponse.isValid()) {
                        return Mono.error(new FilterException("AUTH002", "令牌验证失败"));
                    }
                    
                    // 设置用户信息到上下文
                    chainDTO.getContextInfo().setUserId(authResponse.getUserId());
                    chainDTO.getContextInfo().setUserName(authResponse.getUserName());
                    
                    return Mono.empty();
                })
                .onErrorResume(TimeoutException.class, ex -> 
                    Mono.error(new FilterException("AUTH003", "认证服务超时"))
                );
    }
    
    @Override
    protected String changeResponseBody(String origBody) {
        // 可以在响应中添加用户信息
        return origBody;
    }
    
    @Override
    public int getOrder() {
        return -98; // 认证过滤器应该有较高优先级
    }
}
```

## 注意事项

1. **异常处理**: 不要在 filter 方法中直接抛出异常，而应该返回 `Mono.error()`
2. **超时设置**: 建议为外部调用设置合理的超时时间
3. **错误恢复**: 考虑使用 `onErrorReturn()` 或 `onErrorResume()` 提供降级策略
4. **性能考虑**: 避免在过滤器中进行耗时的同步操作
5. **资源管理**: 确保正确配置 WebClient 的连接池和超时参数

## 迁移指南

从 `AbstractGlobalFilter` 迁移到 `AbstractMonoGlobalFilter`:

1. 将 `filter` 方法的返回类型从 `void` 改为 `Mono<Void>`
2. 将同步的外部调用改为使用 WebClient 等响应式客户端
3. 使用 `Mono.error()` 替代直接抛出异常
4. 在方法末尾返回 `Mono.empty()` 或 `.then()`

## 性能优势

- **非阻塞**: 不会阻塞网关的工作线程
- **更高并发**: 支持更多并发请求处理
- **资源利用**: 更好的线程和内存利用率
- **响应时间**: 减少因外部调用导致的响应延迟
