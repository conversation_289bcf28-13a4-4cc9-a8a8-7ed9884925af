<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cnpc.ymz.bff</groupId>
    <artifactId>kld-cbase</artifactId>
    <!-- 每次变更时需要升级此版本 -->
    <version>1.2.0-SNAPSHOT</version>

    <modules>
        <module>cbase-web</module>
        <module>cbase-gateway</module>
        <module>cbase-rocketmq</module>
        <module>cbase-common</module>
        <module>cbase-job</module>
    </modules>

    <properties>
        <!-- 每次变更时需要升级此版本 -->
        <kld.cbase.version>1.2.0-SNAPSHOT</kld.cbase.version>
        <!-- 架构组基础组件版本号 -->
        <kld.foundation.version>1.1.8</kld.foundation.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven.springboot.version>2.7.10</maven.springboot.version>
        <ymz.nexus-releases.url>https://11.54.91.138/repository/ymz-maven-release/</ymz.nexus-releases.url>
        <ymz.nexus-snapshots.url>https://11.54.91.138/repository/ymz-maven-snapshot/</ymz.nexus-snapshots.url>
        <ymz.nexus.url>https://11.54.91.138/repository/ymz-maven-public/</ymz.nexus.url>

    </properties>

    <packaging>pom</packaging>

    <!-- 引入架构组组件 -->
    <parent>
        <groupId>com.kld.foundation</groupId>
        <artifactId>kld-foundation-alibaba-parents</artifactId>
        <version>1.1.8</version>
    </parent>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cnpc.ymz.bff</groupId>
                <artifactId>cbase-common</artifactId>
                <version>${kld.cbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cnpc.ymz.bff</groupId>
                <artifactId>cbase-gateway</artifactId>
                <version>${kld.cbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cnpc.ymz.bff</groupId>
                <artifactId>cbase-mysql</artifactId>
                <version>${kld.cbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cnpc.ymz.bff</groupId>
                <artifactId>cbase-redis</artifactId>
                <version>${kld.cbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cnpc.ymz.bff</groupId>
                <artifactId>cbase-rocketmq</artifactId>
                <version>${kld.cbase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cnpc.ymz.bff</groupId>
                <artifactId>cbase-web</artifactId>
                <version>${kld.cbase.version}</version>
            </dependency>
            <dependency>
                <artifactId>kld-foundation-dependencies</artifactId>
                <groupId>com.kld.foundation</groupId>
                <version>${kld.foundation.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>3.18.0</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>3.1.9</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.7</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.10.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>ymz-maven-public</id>
            <name>Team Nexus Repository</name>
            <url>https://11.54.91.138/repository/ymz-maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>


    <!-- 部署构件至仓库设置 -->
    <distributionManagement>
        <repository>
            <id>ymz-maven-releases</id>
            <name>Nexus Releases Repository</name>
            <url>https://11.54.91.138/repository/ymz-maven-release/</url>
        </repository>
        <snapshotRepository>
            <id>ymz-maven-snapshot</id>
            <name>Nexus Snapshots Repository</name>
            <url>https://11.54.91.138/repository/ymz-maven-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>


</project>